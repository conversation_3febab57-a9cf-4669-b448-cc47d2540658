package com.dl.aiservice.biz.manager.cos.impl;

import cn.hutool.core.io.FileUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.manager.cos.ObsFileUploadManager;
import com.obs.services.ObsClient;
import com.obs.services.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * @ClassName CosFileUploadManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:58
 * @Version 1.0
 **/
@Slf4j
@Service
public class ObsFileUploadManagerImpl implements ObsFileUploadManager {

    private final static String FILE_DIR_TEMPLATE = "/temp/visual/tts/";
    private final static String FILE_URL = "https://%s.obs.cn-north-4.myhuaweicloud.com/%s";

    @Override
    public String uploadFile(File file, String type, String path) {
        if (Objects.isNull(file)) {
            return null;
        }
        String fileName = file.getName();
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(FileUtil.extName(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + type;
        }
        if (StringUtils.hasLength(path)) {
            fileName = path + Const.SLASH + fileName;
        } else {
            fileName = FILE_DIR_TEMPLATE + fileName;
        }
        ObsClient obsClient = createCli();
        try {
            PutObjectResult putObjectResult = obsClient.putObject(bucketname, fileName, file);
            log.warn("cos upload requestId={}", putObjectResult.getRequestId());
            return String.format(FILE_URL, bucketname, fileName);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            try {
                obsClient.close();
            } catch (IOException e) {
                log.error("", e);
            }
        }
        return null;
    }


    @Value("${digtal.huawei.ak}")
    private String ak;
    @Value("${digtal.huawei.sk}")
    private String sk;
    @Value("${digtal.huawei.bucketName}")
    private String bucketname;
    @Value("${digtal.huawei.endPoint}")
    private String endPoint;

//    private final String ak = "JFAM0LVHAXJ8V0GL3JML";
//    private final String sk = "iD30R0ADRAfHzIrXkXv0tviwY2S3zX1WvHyKmB0E";
//    private final String bucketname = "baixiaole ";

    private ObsClient createCli() {
        return new ObsClient(ak, sk, endPoint);
    }
}

