package com.dl.aiservice.biz.client.jimu.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TemplateVarInfo {

    String templateId;

    List<UnitInfo> templateVariables;

    List<VarInfo> globalVariables;

}
