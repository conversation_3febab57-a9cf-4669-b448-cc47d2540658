package com.dl.aiservice.biz.client.jimu.dto;

import com.dl.aiservice.biz.client.jimu.VarInfoDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonDeserialize(using = VarInfoDeserializer.class)
public class VarInfo<T> {

    String key;

    String type;

    String name;

    T dataFormat;

    JimuExtInfo extInfo;
}
