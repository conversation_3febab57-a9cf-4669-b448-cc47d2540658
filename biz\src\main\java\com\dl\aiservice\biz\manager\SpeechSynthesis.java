package com.dl.aiservice.biz.manager;

import com.dl.aiservice.biz.manager.voiceclone.microsoft.MicrosoftHandlerManager;
import com.microsoft.cognitiveservices.speech.*;
import org.apache.commons.lang3.StringUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SpeechSynthesis {

    private static final String subscriptionKey = "069f74b08f704acfa478055be4d9ba29";
    private static final String subscriptionRegion = "eastasia"; // 例如 "westus"

    /**
     * 语音快慢ssml
     */
    private static final String ssmlText = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "你好, this is a test with <prosody rate='slow'>slow speech</prosody> and <prosody rate='fast'>fast speech</prosody>." +
            "</voice></speak>";

    /**
     * 多音字
     */
    private static final String ssmlDyz = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "<s>这是一个示例句子，<phoneme alphabet=\"sapi\" ph=\"shan 4\">单</phoneme>通常读作san。</s>" +
            "<s>这是另一个示例，<phoneme alphabet=\"sapi\" ph=\"dan 4\">单</phoneme>则读作dan。</s>" +
            "</voice></speak>";

    /**
     * DL里实际的多音字
     */
    private static final String ssmlDyzDL = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "华策影视在2019年12月5日神奇9<phoneme alphabet=\"py\" ph=\"zhuan3\">转</phoneme>发出下跌九信号，\n提示底部机会之后，7个交易日陆续上涨，累计最大涨幅达到24.7%。" +
            "</voice></speak>";


    /**
     * 音量大小
     */
    private static final String ssmlVolumn = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "<prosody volume='+100.00%'>我是数字人主播，请输入文案体验 </prosody>" +
            "</voice></speak>";

    /**
     * 停顿
     */
    private static final String ssmlBreak = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "<s>这是一个示例句子。</s>" +
            "<break time=\"2000ms\" />" +
            "<s>这是下一个示例句子。</s>" +
            "</voice></speak>";

    /**
     * 数字读法
     */
    private static final String ssmlDigits = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
//            "我是主播张忆南，请输入文案体验 <say-as interpret-as=\"digits\">12345678</say-as>" +
            "我是智能语音云泽，<say-as interpret-as=\"number_digit\">334455</say-as>" +
            "</voice></speak>";

    /**
     * 金额读法
     */
    private static final String ssmlCardinal = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "我是主播张忆南，请输入文案体验 <say-as interpret-as=\"cardinal\">12345678</say-as>" +
            "</voice></speak>";

    /**
     * 金额读法
     */
    private static final String ssmlWord = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
            "<voice name='zh-CN-XiaoxiaoNeural'>" +
            "我是主播张忆南，请输<word>入文文文文案体验</word>" +
            "</voice></speak>";

    public static void main(String[] args) throws InterruptedException, ExecutionException {
        complex();
//        String a = MicrosoftHandlerManager.replaceMicrosoftSsml("我是数字人主播，请输入文案体验");
//        System.out.println(a);

    }

    public static void complex() throws InterruptedException, ExecutionException {
        try {
            String ssmlText = MicrosoftHandlerManager.replaceMicrosoftSsml(ssmlCardinal);
            // 设置配置
            SpeechConfig config = SpeechConfig.fromSubscription(subscriptionKey, subscriptionRegion);
            config.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm);

            // 使用SSML文本初始化合成器
            SpeechSynthesizer synthesizer = new SpeechSynthesizer(config);
            SpeechSynthesisResult result = synthesizer.SpeakSsml(ssmlText);
            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
//                byte[] audioData = result.getAudioData();
//                String filePath = "C:\\Users\\<USER>\\Downloads\\output_audio.wav"; // 输出文件路径
//                generateAudioFile(audioData, filePath);
                System.out.println("Speech synthesized for ssml [" + ssmlText + "]");
            } else if (result.getReason() == ResultReason.Canceled) {
                SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                System.out.println("CANCELED: Reason=" + cancellation.getReason());

                if (cancellation.getReason() == CancellationReason.Error) {
                    System.out.println("CANCELED: ErrorCode=" + cancellation.getErrorCode());
                    System.out.println("CANCELED: ErrorDetails=" + cancellation.getErrorDetails());
                    System.out.println("CANCELED: Did you update the subscription info?");
                }
            }

            // 等待合成完成（这只是为了示例，实际应用中可能需要更复杂的逻辑来处理异步操作）
            Thread.sleep(10000); // 等待10秒钟

        } catch (Exception e) {
            System.out.println("An error occurred: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void easy() throws ExecutionException, InterruptedException {
        SpeechConfig config = SpeechConfig.fromSubscription(subscriptionKey, subscriptionRegion);
// Note: the voice setting will not overwrite the voice element in input SSML.
        config.setSpeechSynthesisVoiceName("zh-CN-XiaoxiaoNeural");
        String text = "你好，这是云希。";

        SpeechSynthesizer synthesizer = new SpeechSynthesizer(config);
        {
            SpeechSynthesisResult result = synthesizer.SpeakTextAsync(text).get();
            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                byte[] audioData = result.getAudioData();
                String filePath = "C:\\Users\\<USER>\\Downloads\\output_audio.wav"; // 输出文件路径
                generateAudioFile(audioData, filePath);
                System.out.println("Speech synthesized for text [" + text + "]");
            } else if (result.getReason() == ResultReason.Canceled) {
                SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                System.out.println("CANCELED: Reason=" + cancellation.getReason());

                if (cancellation.getReason() == CancellationReason.Error) {
                    System.out.println("CANCELED: ErrorCode=" + cancellation.getErrorCode());
                    System.out.println("CANCELED: ErrorDetails=" + cancellation.getErrorDetails());
                    System.out.println("CANCELED: Did you update the subscription info?");
                }
            }

            result.close();
        }
        synthesizer.close();
    }

    public static void generateAudioFile(byte[] audioData, String filePath) {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(audioData);
        } catch (IOException e) {
            System.err.println("写入音频文件时发生错误: " + e.getMessage());
        }
    }
}