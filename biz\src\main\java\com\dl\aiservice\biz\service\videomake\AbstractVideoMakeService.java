package com.dl.aiservice.biz.service.videomake;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HtmlUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManAndVoiceBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceBO;
import com.dl.aiservice.biz.service.videomake.enums.VideoMakeJobErrStatusEnum;
import com.dl.aiservice.biz.service.videomake.jimu.bo.InnerErrNotifyParamBO;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.videomake.BaseMessageDTO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName AbstractVideoMakeService
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 20:14
 * @Version 1.0
 **/
@Slf4j
@Component
public abstract class AbstractVideoMakeService implements VideoMakeService {

    @Resource
    private AiConfig aiConfig;

    @Override
    public void doMake(String tenantCode, MediaProduceChannelEnum mediaProduceChannel, BaseMessageDTO message,
            DaVirtualManAndVoiceBO daVirtualManAndVoice, Long workBizId, Long videoTaskJobId) {
        try {
            checkMessage(message);
            if (checkSameSource(mediaProduceChannel, daVirtualManAndVoice)) {
                // 数字分身、声音 与 合成商 渠道一致
                makeVideoHomology(tenantCode, message, daVirtualManAndVoice, workBizId, videoTaskJobId);
                return;
            }
            if (Objects.isNull(daVirtualManAndVoice.getDaVirtualManScenes())) {
                // 第三方声音照片版
                makeVideoByThirdTTS(tenantCode, message, daVirtualManAndVoice.getDaVirtualVoice(), workBizId,
                        videoTaskJobId);
                return;
            }
            // 第三方数字分身版
            makeVideoByThirdVirtualMan(message, daVirtualManAndVoice, workBizId, videoTaskJobId, tenantCode);
        } catch (BusinessServiceException e) {
            log.error("快视频合成异常！", e);
            innerErrNotify(InnerErrNotifyParamBO.builder()
                    .videoTaskJobId(videoTaskJobId)
                    .worksBizId(workBizId)
                    .jobStatus(Integer.valueOf(e.getErrCode()))
                    .failReason(e.getMessage())
                    .callbackUrl(message.getCallbackUrl())
                    .build());
        } catch (Exception e) {
            log.error("快视频合成异常！", e);
            innerErrNotify(InnerErrNotifyParamBO.builder()
                    .videoTaskJobId(videoTaskJobId)
                    .worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_49.getCode())
                    .callbackUrl(message.getCallbackUrl())
                    .build());
        }
    }

    /**
     * 判断数字分身、声音是否与合成商渠道同源
     *
     * @param mediaProduceChannel
     * @param daVirtualManAndVoice
     * @return
     */
    private boolean checkSameSource(MediaProduceChannelEnum mediaProduceChannel,
            DaVirtualManAndVoiceBO daVirtualManAndVoice) {
        DaVirtualManScenesBO daVirtualManScenesBO = daVirtualManAndVoice.getDaVirtualManScenes();
        DaVirtualVoiceBO daVirtualVoice = daVirtualManAndVoice.getDaVirtualVoice();
        if (Objects.isNull(daVirtualManScenesBO)) {
            // 照片版
            return Objects.equals(daVirtualVoice.getChannel(), mediaProduceChannel.getCode());
        }
        // 数字分身
        return Objects.equals(daVirtualManScenesBO.getChannel(), mediaProduceChannel.getCode()) && Objects.equals(
                daVirtualManScenesBO.getChannel(), daVirtualVoice.getChannel());
    }

    /**
     * 根据不同的合成商做不同的处理判断
     *
     * @param message
     */
    public abstract void checkMessage(BaseMessageDTO message);

    /**
     * 同源的视频合成处理方式
     *
     * @param message
     * @param daVirtualManAndVoice
     * @return
     */
    public abstract void makeVideoHomology(String tenantCode, BaseMessageDTO message,
            DaVirtualManAndVoiceBO daVirtualManAndVoice, Long workBizId, Long videoTaskJobId);

    /**
     * 第三方声音 - 照片版视频合成
     *
     * @param message
     * @param daVirtualVoice
     * @return
     */
    public abstract void makeVideoByThirdTTS(String tenantCode, BaseMessageDTO message, DaVirtualVoiceBO daVirtualVoice,
            Long workBizId, Long videoTaskJobId);

    /**
     * 第三方数字人 - 视频合成
     *
     * @param message
     * @param daVirtualManAndVoice
     * @return
     */
    public abstract void makeVideoByThirdVirtualMan(BaseMessageDTO message, DaVirtualManAndVoiceBO daVirtualManAndVoice,
            Long workBizId, Long videoTaskJobId, String tenantCode);

    protected String toStringValue(Object value) {
        return Objects.isNull(value) ? StringUtils.EMPTY : String.valueOf(value);
    }

    protected Number toNumber(String str) {
        if (str.matches("-?\\d+")) { // 如果字符串只包含数字，包括正负号
            if (str.length() <= 10) { // 如果字符串长度小于等于10，则转换为整数类型
                return Integer.parseInt(str);
            } else { // 否则转换为长整型类型
                return Long.parseLong(str);
            }
        } else if (str.matches("-?\\d+(\\.\\d+)?")) { // 如果字符串包含小数点，包括正负号
            if (str.length() <= 14) { // 如果字符串长度小于等于14，则转换为单精度浮点数类型
                return Float.parseFloat(str);
            } else { // 否则转换为双精度浮点数类型
                return Double.parseDouble(str);
            }
        } else { // 如果字符串不是数字格式，则返回null
            return null;
        }
    }

    protected String getCallbackHost() {
        String callback = aiConfig.getCallbackHost();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }

    protected void predicate(boolean condition, String errCode, String errMsg) {
        Assert.isTrue(condition, () -> BusinessServiceException.getInstance(errCode, errMsg));
    }

    protected String cleanStr(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        // 第三方供应商暂不支持 ssml 语法，统一去除
        return HtmlUtil.cleanHtmlTag(value);
    }
}
