package com.dl.aiservice.web.controller.digital.heygen;

import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.heygen.HeyGenDigitalService;
import com.dl.aiservice.web.controller.digital.heygen.param.HeyGenDigitalTrainParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/digital/heygen")
public class HeyGenDigitalController {

    @Autowired
    private HeyGenDigitalService heyGenDigitalService;

    @PostMapping("/train")
    @NotAuth
    public void train(@RequestBody HeyGenDigitalTrainParam param){
        CreateTrainingResponseDTO training = heyGenDigitalService.training(param.getUrl(), param.getName(),
                param.getGender(), param.getTenantCode());
    }

}
