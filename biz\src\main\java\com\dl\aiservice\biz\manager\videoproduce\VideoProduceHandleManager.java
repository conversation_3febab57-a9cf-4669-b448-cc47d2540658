package com.dl.aiservice.biz.manager.videoproduce;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.framework.common.model.ResultModel;

public interface VideoProduceHandleManager {
    /**
     * 视频合成
     *
     * @param paramDTO
     * @return
     */
    ResultModel<VideoProduceResponseDTO> produce(VideoProduceParamDTO paramDTO);

    ServiceChannelEnum getEnum();
}
