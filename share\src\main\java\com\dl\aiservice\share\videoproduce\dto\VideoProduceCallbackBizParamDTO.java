package com.dl.aiservice.share.videoproduce.dto;

import com.dl.aiservice.share.videoproduce.dto.jimu.JobResultDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-14 22:54
 */
@Data
@ApiModel("视频合成回调业务DTO")
public class VideoProduceCallbackBizParamDTO {

    /**
     * 第三方媒体id
     */
    private String extJobId;

    /**
     * 上层业务作品唯一标识
     */
    private Long worksBizId;

    /**
     * 任务状态：1 合成中；0 合成完成；-1 合成失败
     *
     * @see com.dl.aiservice.share.enums.MediaProduceJobStatusEnum
     */
    private Integer jobStatus;

    /**
     * 渠道：0-新华智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 第三方状态
     * <p>
     * 若channel为0时：
     * Initial(0, "作业初始化"),
     * Build_Success(1, "构建成功"),
     * Build_Fail(11, "构建失败"),
     * Trans_Success(2, "协议转换成功"),
     * Trans_Fail(21, "协议转换失败"),
     * Synthesis_Create(3, "发起合成任务"),
     * Synthesis_Success(4, "合成任务成功"),
     * Synthesis_Fail(41, "合成任务失败"),
     */
    private Integer extJobStatus;

    /**
     * 合成发起时间
     */
    private Long synthStarted;

    /**
     * 合成完成时间
     */
    private Long synthUpdated;

    private String failReason;

    /**
     * 视频结果
     */
    private JobResultDTO jobResult;

}
