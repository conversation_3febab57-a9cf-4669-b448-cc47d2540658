package com.dl.aiservice.share.videoproduce.dto.jimu;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 新华志云 openapi配置
 */
@Data
@ConfigurationProperties(prefix = "xh.jimu")
@Configuration
public class JimuProperties {
    private String accessKey;
    private String secretKey;
    private String url;
}
