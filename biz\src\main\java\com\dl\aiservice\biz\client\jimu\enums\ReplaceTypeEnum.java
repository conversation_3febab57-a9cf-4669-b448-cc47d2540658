package com.dl.aiservice.biz.client.jimu.enums;

public enum ReplaceTypeEnum {
    REMOVE_VAR("removeVariable", "移除元素", 1),
    REMOVE_CARD("removeCard", "移除卡⽚", 2),
    UNDO("undo", "默认,不操作", 0),
    REMOVE_ALL("removeAll", "模板不渲染", 3);

    private String code;
    private String desc;

    private int priority;

    ReplaceTypeEnum(String code, String desc, int priority) {
        this.code = code;
        this.desc = desc;
        this.priority = priority;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int getPriority() {
        return priority;
    }
}