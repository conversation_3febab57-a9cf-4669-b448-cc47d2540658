package com.dl.aiservice.biz.service.videomake.jimu.bo;

import com.dl.aiservice.biz.client.jimu.dto.JimuExtInfo;
import com.dl.aiservice.biz.client.jimu.enums.ReplaceTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VarBO<T> {

    String key;

    T value;

    String replaceType;

    JimuExtInfo extInfo;

    String tts;

    public VarBO(String key, T value, String replaceType) {
        this.key = key;
        this.value = value;
        this.replaceType = replaceType;
        //replaceType是只在value为空才会生效
        if (!StringUtils.equals(ReplaceTypeEnum.UNDO.getCode(), replaceType)) {
            this.value = null;
        }
    }

    public VarBO(String key, T value, String replaceType, JimuExtInfo extInfo) {
        this.key = key;
        this.value = value;
        this.replaceType = replaceType;
        this.extInfo = extInfo;
        //replaceType是只在value为空才会生效
        if (!StringUtils.equals(ReplaceTypeEnum.UNDO.getCode(), replaceType)) {
            this.value = null;
        }
    }
}
