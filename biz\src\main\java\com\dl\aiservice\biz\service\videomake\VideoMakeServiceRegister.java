package com.dl.aiservice.biz.service.videomake;

import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class VideoMakeServiceRegister implements BeanFactoryPostProcessor {

    private ConfigurableListableBeanFactory beanFactory;

    private static final Map<MediaProduceChannelEnum, VideoMakeService> VIDEO_MAKE_SERVICE =
            new ConcurrentHashMap<>();

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    void init() {
        String[] videoMakeServiceArr = this.beanFactory.getBeanNamesForType(VideoMakeService.class);
        if (videoMakeServiceArr.length != 0) {
            for (String sendApi : videoMakeServiceArr) {
                register((VideoMakeService) beanFactory.getBean(sendApi));
            }
        }
    }

    private void register(VideoMakeService videoMakeService) {
        if (Objects.nonNull(videoMakeService)) {
            Assert.notNull(videoMakeService.getEnum(),
                    "enum() cannot be empty beanName:" + videoMakeService.getClass().getName());
            VIDEO_MAKE_SERVICE.put(videoMakeService.getEnum(), videoMakeService);
        }
    }

    VideoMakeService get(MediaProduceChannelEnum typeEnum) {
        return VIDEO_MAKE_SERVICE.get(typeEnum);
    }
}
