package com.dl.aiservice.biz.service.videomake;

import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@AllArgsConstructor
public class VideoMakeServiceHelper {

    private final VideoMakeServiceRegister videoMakeServiceRegister;

    @PostConstruct
    public void init() {
        videoMakeServiceRegister.init();
    }

    public VideoMakeService get(MediaProduceChannelEnum typeEnum) {
        return videoMakeServiceRegister.get(typeEnum);
    }
}
