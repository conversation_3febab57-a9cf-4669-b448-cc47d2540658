package com.dl.aiservice.share.videoproduce.dto.jimu;

public enum ReplaceType {
    REMOVE_VAR("removeVariable", "移除元素", 1),
    REMOVE_CARD("removeCard", "移除卡⽚", 2),
    UNDO("undo", "默认,不操作", 0),
    REMOVE_ALL("removeAll", "模板不渲染", 3);

    private String code;
    private String desc;

    private int priority;

    ReplaceType(String code, String desc, int priority) {
        this.code = code;
        this.desc = desc;
        this.priority = priority;
    }

    public static ReplaceType getInstance(String code) {
        for (ReplaceType replaceType : ReplaceType.values()) {
            if (replaceType.code.equals(code)) {
                return replaceType;
            }
        }
        return ReplaceType.UNDO;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int getPriority() {
        return priority;
    }
}