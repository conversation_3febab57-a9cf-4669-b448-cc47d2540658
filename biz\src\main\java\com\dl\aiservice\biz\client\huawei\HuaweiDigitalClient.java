package com.dl.aiservice.biz.client.huawei;

import com.dl.aiservice.biz.client.huawei.intercepter.HuaweiDigitalInterceptor;
import com.dl.aiservice.biz.client.huawei.req.HuaweiTokenRequest;
import com.dl.aiservice.biz.client.huawei.req.HuaweiTtsRequest;
import com.dl.aiservice.biz.client.huawei.resp.HuaweiTtsResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.dtflys.forest.http.ForestResponse;

@BaseRequest(baseURL = "https://iam.cn-north-4.myhuaweicloud.com", interceptor = HuaweiDigitalInterceptor.class)
public interface HuaweiDigitalClient {

    String GET_TOKEN = "/v3/auth/tokens";

    String X_Auth_Token = "X-Auth-Token";

    @Post(url = "/v3/auth/tokens")
    ForestResponse<String> getToken(@JSONBody HuaweiTokenRequest huaweiTokenRequest);

    @Post(url = "/v1/{project_id}/voice-clone/inference-jobs")
    HuaweiTtsResponse cloneTts(@Var("project_id") String projectId, @JSONBody HuaweiTtsRequest huaweiTokenRequest);

}
