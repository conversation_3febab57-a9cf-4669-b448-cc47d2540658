package com.dl.aiservice.biz.service.videomake;

import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManAndVoiceBO;
import com.dl.aiservice.biz.service.videomake.jimu.bo.InnerErrNotifyParamBO;
import com.dl.aiservice.share.digitalman.DigitalManVideoGenResultDTO;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.videomake.BaseMessageDTO;

import java.util.List;

/**
 * @ClassName VideoMakeService
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 16:13
 * @Version 1.0
 **/
public interface VideoMakeService {

    String KEY_TEMP_MSG_PREFIX = "video_make_temp_message_";
    Integer REDIS_TEMP_MSG_TIMEOUT = 2 * 60 * 60;

    MediaProduceChannelEnum getEnum();

    /**
     * 合成视频
     *
     * @param message
     * @param daVirtualManAndVoice
     * @return 内部jobId
     */
    void doMake(String tenantCode, MediaProduceChannelEnum mediaProduceChannel, BaseMessageDTO message,
            DaVirtualManAndVoiceBO daVirtualManAndVoice, Long workBizId, Long videoTaskJobId);

    /**
     * 第三方数字人视频合成完成之后需要处理的逻辑
     *
     * @param videoTaskJobId
     * @param videoList
     */
    void afterThirdVirtualManDone(String tenantCode, Long videoTaskJobId, List<DigitalManVideoGenResultDTO> videoList);

    /**
     * 内部错误导致视频合成失败时需要触发回调通知
     *
     * @param param
     */
    void innerErrNotify(InnerErrNotifyParamBO param);
}
