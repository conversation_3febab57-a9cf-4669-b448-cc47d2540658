package com.dl.aiservice.biz.client.huawei.intercepter;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.huawei.HuaweiDigitalClient;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.service.digital.huawei.HuaweiDigitalService;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

@Slf4j
public class HuaweiDigitalInterceptor implements Interceptor {

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        String requestUrl = request.getMethod().getMetaRequest().getUrl();
        if (requestUrl.startsWith(HuaweiDigitalClient.GET_TOKEN)) {
            return Boolean.TRUE;
        }

        HuaweiDigitalService digitalService = ApplicationContextUtils.getContext().getBean(HuaweiDigitalService.class);
        String token = digitalService.getToken();
        Assert.isTrue(StringUtils.isNotEmpty(token), "华为云获取token失败");
        request.addHeader(HuaweiDigitalClient.X_Auth_Token, token);
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        throw BusinessServiceException.getInstance(ex.getMessage());
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", JSONUtil.toJsonStr(request.getBody().nameValuesMapWithObject()), response.getContent());
    }

    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        fillErrMsg(data);
    }

    private void fillErrMsg(Object data) {
//        if (!(data instanceof IflyBaseResponse)) {
//            return;
//        }
//        IflyBaseResponse resp = (IflyBaseResponse) data;
//        IflyErrCodeEnum errCodeEnum = IflyErrCodeEnum.errorCode(resp.getCode());
//        if (errCodeEnum != IflyErrCodeEnum.ERROR_CODE_0) {
//            throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode(), errCodeEnum.getErrorDesc());
//        }
    }
}
