package com.dl.aiservice.web.controller.videoproduce;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.callback.AliyunVideoProduceCallBackClient;
import com.dl.aiservice.biz.client.callback.JimuVideoProduceCallBackClient;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.manager.videoproduce.VideoProduceHandleManager;
import com.dl.aiservice.biz.register.VideoProduceHelper;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.videoproduce.dto.VideoProduceCallbackBizParamDTO;
import com.dl.aiservice.share.videoproduce.dto.aliyun.ProduceMediaCompleteParamDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.JimuProperties;
import com.dl.aiservice.share.videoproduce.dto.jimu.JimuTaskCallbackParamDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.JobResultDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.util.Objects;

/**
 * @ClassName VoiceCloneServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/3/13 9:54
 * @Version 1.0
 **/
@Component
@Slf4j
public class VoiceProduceProcessor {

    @Resource
    private VideoProduceHelper videoProduceHelper;

    @Resource
    private ChannelUtil channelUtil;

    @Resource
    private JimuProperties jimuProperties;

    @Resource
    private JimuVideoProduceCallBackClient jimuVideoProduceCallBackClient;

    @Resource
    private CallbackLogManager callbackLogManager;

    @Resource
    private AliyunVideoProduceCallBackClient aliyunVideoProduceCallBackClient;

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Value("${videoProduce.notifyUrl.aliyun}")
    private String aliyunVideoNofifyUrl;

    private VideoProduceHandleManager getHandler() {
        MediaProduceChannelEnum e = MediaProduceChannelEnum.getByCode(channelUtil.getChannel());
        Assert.notNull(e, "该渠道暂不支持视频生产");
        return videoProduceHelper.get(ServiceChannelEnum.getByCode(e.getCode()));
    }

    public ResultModel<VideoProduceResponseDTO> videoProduce(VideoProduceParamDTO request) {
        return getHandler().produce(request);
    }

    public boolean jimiValidate(long timestamp, String signature) {
        String needSignatureStr = jimuProperties.getSecretKey() + "$" + timestamp + "$" + jimuProperties.getAccessKey();
        boolean isEqual = signature.equals(md5(needSignatureStr));
        if (!isEqual) {
            log.error("签名校验失败!timestamp:{},signature:{}", timestamp, signature);
        }
        return isEqual;
    }

    public static String md5(String needSignatureStr) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(needSignatureStr.getBytes("UTF-8"));
            byte[] summery = md5.digest();
            StringBuilder md5StrBuilder = new StringBuilder();

            for (byte aSummery : summery) {
                if (Integer.toHexString(255 & aSummery).length() == 1) {
                    md5StrBuilder.append("0").append(Integer.toHexString(255 & aSummery));
                } else {
                    md5StrBuilder.append(Integer.toHexString(255 & aSummery));
                }
            }
            return md5StrBuilder.toString();
        } catch (Exception e) {
            System.err.println("获取签名串失败" + e);
            return "";
        }
    }

    public ResultModel<Boolean> handleJimuCallback(long timestamp, String signature, JimuTaskCallbackParamDTO param) {
        Assert.isTrue(Objects.nonNull(param) && StringUtils.isNotBlank(param.getJobId()), "入参错param为空或jobId不存在");
        Assert.isTrue(jimiValidate(timestamp, signature), "签名校验失败");

        //判断是否需要回调
        MediaProduceJobPO mediaProduceJobPO =
                mediaProduceJobManager.lambdaQuery().eq(MediaProduceJobPO::getExtJobId, param.getJobId()).one();
        Assert.notNull(mediaProduceJobPO, "媒体合成记录表不存在");
        log.info("新华智云视频合成回调，param={},mediaProduceJobPO:{}", JSONUtil.toJsonStr(param),
                JSONUtil.toJsonStr(mediaProduceJobPO));

        //是否已超时
        Boolean hasTimeout =
                !MediaProduceJobTimeoutStatusEnum.UN.getStatus().equals(mediaProduceJobPO.getTimeoutStatus());
        //更新mediaProduceJobPO
        this.fillMediaProduceJobPO(param, mediaProduceJobPO, hasTimeout);
        mediaProduceJobManager.updateById(mediaProduceJobPO);

        //已超时则不回调业务
        if (hasTimeout) {
            return ResultModel.success(true);
        }

        VideoProduceCallbackBizParamDTO callbackBizParamDTO = new VideoProduceCallbackBizParamDTO();
        callbackBizParamDTO.setWorksBizId(mediaProduceJobPO.getWorksBizId());
        callbackBizParamDTO.setJobStatus(mediaProduceJobPO.getStatus());
        if (Objects.nonNull(mediaProduceJobPO.getVideoTaskJobId())) {
            callbackBizParamDTO.setExtJobId(mediaProduceJobPO.getVideoTaskJobId().toString());
        } else {
            callbackBizParamDTO.setExtJobId(mediaProduceJobPO.getExtJobId());
        }
        callbackBizParamDTO.setChannel(mediaProduceJobPO.getChannel());
        callbackBizParamDTO.setExtJobStatus(param.getJobStatus());
        JobResultDTO jobResult = param.getJobResult();
        callbackBizParamDTO.setJobResult(jobResult);
        callbackBizParamDTO.setSynthStarted(param.getSynthStarted());
        callbackBizParamDTO.setSynthUpdated(param.getSynthUpdated());
        if (Objects.nonNull(jobResult)) {
            callbackBizParamDTO.setFailReason(jobResult.getMessage());
        }
        //回调业务
        ResultModel callbackResult =
                jimuVideoProduceCallBackClient.callback(mediaProduceJobPO.getCallbackUrl(), callbackBizParamDTO);

        //记录日志
        CallbackLogPO callbackLog =
                initCallbackLog(ServiceChannelEnum.XHZY, JSONUtil.toJsonStr(param), param.getJobId());
        callbackLog.setStatus(callbackResult.isSuccess() ? Const.ONE : Const.TWO);
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult));
        callbackLogManager.save(callbackLog);
        return ResultModel.success(true);
    }

    private void fillMediaProduceJobPO(JimuTaskCallbackParamDTO param, MediaProduceJobPO mediaProduceJobPO,
            Boolean hasTimeout) {
        JobResultDTO jobResult = param.getJobResult();

        //先判断是否已经超时，若已超时，则修改timeout_status，不处理status
        if (hasTimeout) {
            if (param.getJobStatus() == 4) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
                if (Objects.nonNull(jobResult)) {
                    mediaProduceJobPO.setMediaUrl(jobResult.getUrl());
                    mediaProduceJobPO.setDuration(jobResult.getDuration());
                    mediaProduceJobPO.setCoverUrl(
                            Objects.nonNull(jobResult.getCoverUrl()) ? jobResult.getCoverUrl().getDefaultUrl() : "");
                }
                return;
            }
            if (param.getJobStatus() == 11 || param.getJobStatus() == 21 || param.getJobStatus() == 41) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
                return;
            }
            return;
        }

        if (param.getJobStatus() == 4) {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
            if (Objects.nonNull(jobResult)) {
                mediaProduceJobPO.setMediaUrl(jobResult.getUrl());
                mediaProduceJobPO.setDuration(jobResult.getDuration());
                mediaProduceJobPO.setCoverUrl(
                        Objects.nonNull(jobResult.getCoverUrl()) ? jobResult.getCoverUrl().getDefaultUrl() : "");
            }
        } else if (param.getJobStatus() == 11 || param.getJobStatus() == 21 || param.getJobStatus() == 41) {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
        }
    }

    private CallbackLogPO initCallbackLog(ServiceChannelEnum e, String extCallbackRespBody, String recordId) {
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(recordId);
        // 1.视频合成记录
        callbackLog.setCallbackType(Const.ONE);
        callbackLog.setChannel(e.getCode());
        callbackLog.setExtCallbackRespBody(extCallbackRespBody);
        return callbackLog;
    }

    @Deprecated
    public ResultModel<Boolean> handleAliyunCallback(ProduceMediaCompleteParamDTO param) {
        log.info("阿里云视频合成回调，param={}", JsonUtils.toJSON(param));

        CallbackLogPO callbackLog = initCallbackLog(ServiceChannelEnum.ALIYUN, JSONUtil.toJsonStr(param),
                param.getMessageBody().getJobId());
        ResultModel callbackResult = aliyunVideoProduceCallBackClient.callback(aliyunVideoNofifyUrl, param);
        if (callbackResult.isSuccess()) {
            callbackLog.setStatus(Const.ONE);
        } else {
            callbackLog.setStatus(Const.TWO);
        }
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult));
        callbackLogManager.save(callbackLog);
        return ResultModel.success(true);
    }
}
