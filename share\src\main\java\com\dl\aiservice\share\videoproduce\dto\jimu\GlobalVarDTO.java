package com.dl.aiservice.share.videoproduce.dto.jimu;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GlobalVarDTO<T> {
    /**
     * 变量 key, 卡⽚上变量的唯⼀标识
     */
    String key;

    /**
     * 变量类型, 标明使⽤该变量的组件类型
     */
    String type;

    /**
     * 变量描述, 说明该变量需要使⽤什么数据作为⼊参
     */
    String name;

    /**
     * 可视化组件变量描述
     * 当组件为可视化组件类型时, 对该组件
     * ⼊参格式的说明,不同组件的数据格式也
     * 会不同, 可能是数组或对象等
     * 例如下⾯的样例, 折线图组件需要数组
     * 数据, 公司简介需要对象数据
     */
    T value;

}

