package com.dl.aiservice.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.mq.producer.AiDigitalManProducer;
import com.dl.aiservice.biz.register.DigitalHelper;
import com.dl.aiservice.biz.service.digital.dto.req.ProgressRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.share.enums.DigitalManChannelEnum;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @describe: 数字人合成进度查询消费者
 * @date: 2023/5/16 13:58
 */
@Slf4j
@Component
public class AiProgressDigitalManConsumer {

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private DigitalHelper digitalHelper;
    @Resource
    private AiDigitalManProducer aiDigitalManProducer;

    @StreamListener("digitalprogressconsumer")
    public void getProgressAndCallBack(@Payload ProgressRequestDTO progressRequestDTO, @Header("rocketmq_MESSAGE_ID") String messageId) {
        log.debug("AiDigitalManConsumer.getProgressAndCallBack messageId={}, input={}", messageId, JsonUtils.toJSON(progressRequestDTO));

        LambdaQueryWrapper<MediaProduceJobPO> queryContWrappers = Wrappers.lambdaQuery(MediaProduceJobPO.class)
                .eq(MediaProduceJobPO::getExtJobId, progressRequestDTO.getTaskId())
                .eq(MediaProduceJobPO::getWorksBizId, progressRequestDTO.getWorksBizId());
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(queryContWrappers);
        if (Objects.isNull(mediaProduceJobPO)) {
            return;
        }
        if (!mediaProduceJobPO.getStatus().equals(MediaProduceJobStatusEnum.ING.getStatus())) {
            return;
        }


        TaskRequestDTO taskRequestDTO = new TaskRequestDTO();
        taskRequestDTO.setTaskId(mediaProduceJobPO.getExtJobId());
        taskRequestDTO.setWorksBizId(mediaProduceJobPO.getWorksBizId());
        taskRequestDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
        ProgressResponseDTO progressDTO = digitalHelper.get(ServiceChannelEnum.getByCode(mediaProduceJobPO.getChannel())).getProgress(taskRequestDTO);


        if (SynthesisStatusEnum.SUCCESS.getCode().equals(progressDTO.getSynthesisStatus())) {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
        } else if (SynthesisStatusEnum.FAIL.getCode().equals(progressDTO.getSynthesisStatus())
                || SynthesisStatusEnum.CANCEL.getCode().equals(progressDTO.getSynthesisStatus())
                || SynthesisStatusEnum.TASK_FILE.getCode().equals(progressDTO.getSynthesisStatus())) {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
        } else {
            if (progressRequestDTO.getCount() <= 120) {
                //120*5s 十分钟
                progressRequestDTO.setCount(progressRequestDTO.getCount() + 1);
                aiDigitalManProducer.getProgressAndCallBackDelayed(progressRequestDTO);
                return;
            }
        }

        mediaProduceJobPO.setResponseDt(new Date());
        mediaProduceJobPO.setMediaUrl(progressDTO.getVideoUrl());
        mediaProduceJobPO.setDuration(
                StringUtils.isNotBlank(progressDTO.getDuration()) ? Double.valueOf(progressDTO.getDuration()) : null);

        //判断合成厂商
        if (DigitalManChannelEnum.GUI_JI.getCode().equals(mediaProduceJobPO.getChannel())
                || DigitalManChannelEnum.CJHX_GUIJI.getCode().equals(mediaProduceJobPO.getChannel())) {
            mediaProduceJobPO.setCoverUrl(progressDTO.getCoverUrl());
            mediaProduceJobPO.setMediaName(progressDTO.getVideoName());
        }
        mediaProduceJobManager.updateById(mediaProduceJobPO);

        try {
            //回调业务侧
            DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
            BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);
            log.info("数字人合成业务回调 digitalCallbackDTO={}", JsonUtils.toJSON(digitalCallbackDTO));
            digitalHelper.get(ServiceChannelEnum.getByCode(mediaProduceJobPO.getChannel()))
                    .callBackBiz(mediaProduceJobPO.getTenantCode(), mediaProduceJobPO.getWorksBizId(),
                            mediaProduceJobPO.getCallbackUrl(), digitalCallbackDTO, JSONUtil.toJsonStr(progressDTO));
        } catch (Exception e) {
            log.error("数字人合成回调业务异常 error", e);
        }

    }
}
