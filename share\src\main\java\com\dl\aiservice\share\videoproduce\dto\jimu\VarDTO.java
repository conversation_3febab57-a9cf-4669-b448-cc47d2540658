package com.dl.aiservice.share.videoproduce.dto.jimu;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VarDTO<T> {

    String key;

    T value;

    String replaceType;

    JimuDigitalManInfoDTO extInfo;

    public VarDTO(String key, T value, String replaceType, JimuDigitalManInfoDTO extInfo) {
        this.key = key;
        this.value = value;
        this.replaceType = replaceType;
        this.extInfo = extInfo;
        //replaceType是只在value为空才会生效
        if (!StringUtils.equals(ReplaceType.UNDO.getCode(), replaceType)) {
            this.value = null;
        }
    }
}
