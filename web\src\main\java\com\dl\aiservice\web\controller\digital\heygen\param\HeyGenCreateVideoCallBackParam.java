package com.dl.aiservice.web.controller.digital.heygen.param;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@Slf4j
public class HeyGenCreateVideoCallBackParam implements Serializable {

    @JsonProperty(value = "event_type")
    private String eventType;

    @JsonProperty(value = "event_data")
    private HeyGenCallBackEventData eventData;

    public static HeyGenCreateVideoCallBackParam getCallBackParam(String json) {
    HeyGenCreateVideoCallBackParam param = JSONUtil.toBean(json,
                HeyGenCreateVideoCallBackParam.class);
    log.info("HeyGenCreateVideoCallBackParam = " + JSONUtil.toJsonStr(param));
    return param;
    }
}
