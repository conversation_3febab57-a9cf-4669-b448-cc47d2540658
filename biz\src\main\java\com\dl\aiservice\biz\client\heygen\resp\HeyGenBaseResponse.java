package com.dl.aiservice.biz.client.heygen.resp;

import com.dl.aiservice.biz.client.heygen.enums.HeyGenErrorEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class HeyGenBaseResponse <T> implements Serializable {

    private static final long serialVersionUID = 1961425384297568194L;

    private Integer code;

    private String message;

    private T data;

    public boolean isSuccess(){return HeyGenErrorEnum.Error_CODE_100.getErrorCode().equals(code);}
}
