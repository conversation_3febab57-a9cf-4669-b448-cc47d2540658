package com.dl.aiservice.share.videoproduce.dto.jimu;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JobResultDTO {

    String requestId;

    /**
     * 视频封⾯
     */
    CoverUrlDTO coverUrl;

    /**
     * 视频时长
     */
    Double duration;

    /**
     * 合成任务状态 mgcFinish，
     */
    String taskStatus;

    /**
     * 视频链接
     */
    String url;

    /**
     * 合成消息
     */
    String message;

}
