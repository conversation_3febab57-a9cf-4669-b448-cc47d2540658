package com.dl.aiservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import com.dl.aiservice.biz.service.digital.dto.req.ProgressRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * @describe: 数字人合成请求消息发送
 * @author: zhousx
 * @date: 2023/5/11 13:37
 */
@Slf4j
@Service
public class AiDigitalManProducer {
    @Autowired
    private AiChannels aiChannels;

    public void getProgressAndCallBackDelayed(ProgressRequestDTO progressRequestDTO) {
        Message message = MessageBuilder.withPayload(progressRequestDTO).setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, DelayLevelEnum.FIVE_SECEND.getValue()).build();
        boolean sendResult = aiChannels.digitalprogressproducer().send(message, 1000L);
        log.info("发送数字人查询进度请求的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
    }
}
