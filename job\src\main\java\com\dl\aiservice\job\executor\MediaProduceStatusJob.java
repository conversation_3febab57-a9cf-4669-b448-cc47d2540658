package com.dl.aiservice.job.executor;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.aiservice.biz.client.callback.JimuVideoProduceCallBackClient;
import com.dl.aiservice.biz.client.jimu.JimuWebClient;
import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.client.jimu.dto.JobInfo;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.DateUtil;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTypeEnum;
import com.dl.aiservice.biz.register.DigitalHelper;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.share.enums.DigitalManChannelEnum;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.dto.VideoProduceCallbackBizParamDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.JobResultDTO;
import com.dl.framework.common.model.ResultModel;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-13 10:00
 */
@Component
public class MediaProduceStatusJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(MediaProduceStatusJob.class);

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Resource
    private DigitalHelper digitalHelper;

    @Resource
    private JimuWebClient jimuWebClient;

    @Resource
    private JimuVideoProduceCallBackClient jimuVideoProduceCallBackClient;

    @Resource
    private CallbackLogManager callbackLogManager;

    @Resource
    private ChannelUtil channelUtil;

    private static List<Integer> jobTypeList;

    static {
        jobTypeList = new ArrayList<>();
        jobTypeList.add(MediaProduceJobTypeEnum.VIDEO_PRODUCE.getType());
        jobTypeList.add(MediaProduceJobTypeEnum.DIGITAL_MAN.getType());
    }

    /**
     * 媒体合成记录状态处理的定时任务
     */
    @XxlJob("mediaProduceStatusJobHandler")
    public void mediaProduceStatusJobHandler() {
        Date now = new Date();
        //查询创建时间是过去5-35分钟内的合成中的媒体合成记录
        Date beginDate = DateUtil.addMinute(-35, now);
        Date endDate = DateUtil.addMinute(-5, now);
        LOGGER.info("开始执行媒体合成记录状态处理的定时任务,beginDate:{},endDate:{}", beginDate, endDate);

        //分页查询
        Page<MediaProduceJobPO> pageContQuery = new Page<>(1, 100);
        LambdaQueryWrapper<MediaProduceJobPO> queryContWrappers = Wrappers.lambdaQuery(MediaProduceJobPO.class)
                .ge(MediaProduceJobPO::getCreateDt, beginDate).le(MediaProduceJobPO::getCreateDt, endDate)
                .in(MediaProduceJobPO::getJobType, jobTypeList)
                .eq(MediaProduceJobPO::getStatus, MediaProduceJobStatusEnum.ING.getStatus());
        IPage<MediaProduceJobPO> pageContResult = mediaProduceJobManager.page(pageContQuery, queryContWrappers);

        //数字人合成记录列表
        List<MediaProduceJobPO> digitalManJobList = new ArrayList<>();
        //视频合成记录列表
        List<MediaProduceJobPO> videoProduceJobList = new ArrayList<>();

        while (pageContQuery.getCurrent() <= pageContResult.getPages()) {

            //区分
            this.distinguish(pageContResult.getRecords(), digitalManJobList, videoProduceJobList);

            digitalManJobList.forEach(this::handleSingleDigitalManMediaProduceJob);

            videoProduceJobList.forEach(this::handleSingleVideoMakeMediaProduceJob);

            if (pageContQuery.getCurrent() == pageContResult.getPages()) {
                break;
            }
            //查下一页
            pageContQuery.setCurrent(pageContQuery.getCurrent() + 1);
            pageContResult = mediaProduceJobManager.page(pageContQuery, queryContWrappers);
        }
        LOGGER.info("执行媒体合成记录状态处理的定时任务完成,beginDate:{},endDate:{}", beginDate, endDate);
    }

    private void distinguish(List<MediaProduceJobPO> sourceList, List<MediaProduceJobPO> digitalManJobList,
                             List<MediaProduceJobPO> videoProduceJobList) {
        digitalManJobList.clear();
        videoProduceJobList.clear();

        sourceList.forEach(jobPO -> {
            if (MediaProduceJobTypeEnum.DIGITAL_MAN.getType().equals(jobPO.getJobType())) {
                digitalManJobList.add(jobPO);
            } else if (MediaProduceJobTypeEnum.VIDEO_PRODUCE.getType().equals(jobPO.getJobType())) {
                videoProduceJobList.add(jobPO);
            }
        });
    }

    /**
     * 处理单条数字人合成记录
     *
     * @param mediaProduceJobPO
     */
    private void handleSingleDigitalManMediaProduceJob(MediaProduceJobPO mediaProduceJobPO) {
        try {
            channelUtil.init(mediaProduceJobPO.getTenantCode(), mediaProduceJobPO.getChannel());

            //调用外部接口查询合成状态
            TaskRequestDTO taskRequestDTO = new TaskRequestDTO();
            taskRequestDTO.setTaskId(mediaProduceJobPO.getExtJobId());
            taskRequestDTO.setWorksBizId(mediaProduceJobPO.getWorksBizId());
            taskRequestDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
            ProgressResponseDTO progressDTO = digitalHelper
                    .get(ServiceChannelEnum.getByCode(mediaProduceJobPO.getChannel())).getProgress(taskRequestDTO);

            //先判断合成中和排队状态
            if (SynthesisStatusEnum.LINE_UP.getCode().equals(progressDTO.getSynthesisStatus())
                    || SynthesisStatusEnum.MAKING.getCode().equals(progressDTO.getSynthesisStatus())) {
                //如果记录的创建时间没超过30分钟，不处理
                if (DateUtil.between(mediaProduceJobPO.getCreateDt(), new Date(), Calendar.MINUTE) < 60) {
                    return;
                }
                //记录的创建时间超过30分钟，置为失败状态
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
                mediaProduceJobPO.setFailReason("合成时间超过我方阈值");
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT.getStatus());
                LOGGER.info("该记录的创建时间超过30分钟,置为失败状态,,,mediaProduceJobPO:{}", JSONUtil.toJsonStr(mediaProduceJobPO));
            } else if (SynthesisStatusEnum.SUCCESS.getCode().equals(progressDTO.getSynthesisStatus())) {
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
            } else if (SynthesisStatusEnum.FAIL.getCode().equals(progressDTO.getSynthesisStatus())
                    || SynthesisStatusEnum.CANCEL.getCode().equals(progressDTO.getSynthesisStatus())
                    || SynthesisStatusEnum.TASK_FILE.getCode().equals(progressDTO.getSynthesisStatus())) {
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
                mediaProduceJobPO.setFailReason(progressDTO.getFailMessage());
            }

            mediaProduceJobPO.setResponseDt(new Date());
            mediaProduceJobPO.setMediaUrl(progressDTO.getVideoUrl());
            mediaProduceJobPO.setDuration(StringUtils.isNotBlank(progressDTO.getDuration()) ?
                    Double.valueOf(progressDTO.getDuration()) :
                    null);

            //判断合成厂商
            if (DigitalManChannelEnum.GUI_JI.getCode().equals(mediaProduceJobPO.getChannel())
                    || DigitalManChannelEnum.CJHX_GUIJI.getCode().equals(mediaProduceJobPO.getChannel())) {
                mediaProduceJobPO.setCoverUrl(progressDTO.getCoverUrl());
                mediaProduceJobPO.setMediaName(progressDTO.getVideoName());
            }
            mediaProduceJobManager.updateById(mediaProduceJobPO);

            //回调业务侧
            DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
            BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);
            digitalHelper.get(ServiceChannelEnum.getByCode(mediaProduceJobPO.getChannel()))
                    .callBackBiz(mediaProduceJobPO.getTenantCode(), mediaProduceJobPO.getWorksBizId(),
                            mediaProduceJobPO.getCallbackUrl(), digitalCallbackDTO, JSONUtil.toJsonStr(progressDTO));
        } catch (Exception e) {
            LOGGER.error("处理单条数字人合成记录发生异常,mediaProduceJobPO:{},,,,e:{}", JSONUtil.toJsonStr(mediaProduceJobPO), e);
        }
    }

    /**
     * 处理单条视频合成记录
     *
     * @param mediaProduceJobPO
     */
    private void handleSingleVideoMakeMediaProduceJob(MediaProduceJobPO mediaProduceJobPO) {
        try {
            JimuResult<JobInfo> result = jimuWebClient.getVideoInfo(mediaProduceJobPO.getExtJobId());
            JobInfo jobInfo = result.getData();

            //更新mediaProduceJobPO的status
            if (jobInfo.getJobStatus() == 0 || jobInfo.getJobStatus() == 1 || jobInfo.getJobStatus() == 2
                    || jobInfo.getJobStatus() == 3) {
                //如果记录的创建时间没超过30分钟，不处理
                if (DateUtil.between(mediaProduceJobPO.getCreateDt(), new Date(), Calendar.MINUTE) < 30) {
                    return;
                }
                //记录的创建时间超过30分钟，置为失败状态
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT.getStatus());
                mediaProduceJobPO.setFailReason("合成时间超过我方阈值");
                LOGGER.info("该记录的创建时间超过30分钟,置为失败状态,,,mediaProduceJobPO:{}", JSONUtil.toJsonStr(mediaProduceJobPO));
            } else if (jobInfo.getJobStatus() == 4) {
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
                JobResultDTO jobResult = jobInfo.getJobResult();
                if (Objects.nonNull(jobResult)) {
                    mediaProduceJobPO.setMediaUrl(jobResult.getUrl());
                    mediaProduceJobPO.setDuration(jobResult.getDuration());
                    mediaProduceJobPO.setCoverUrl(
                            Objects.nonNull(jobResult.getCoverUrl()) ? jobResult.getCoverUrl().getDefaultUrl() : "");
                }
            } else if (jobInfo.getJobStatus() == 11 || jobInfo.getJobStatus() == 21 || jobInfo.getJobStatus() == 41) {
                mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
            }
            mediaProduceJobManager.updateById(mediaProduceJobPO);

            VideoProduceCallbackBizParamDTO callbackBizParamDTO = new VideoProduceCallbackBizParamDTO();
            callbackBizParamDTO.setWorksBizId(mediaProduceJobPO.getWorksBizId());
            callbackBizParamDTO.setJobStatus(mediaProduceJobPO.getStatus());
            if (Objects.nonNull(mediaProduceJobPO.getVideoTaskJobId())) {
                callbackBizParamDTO.setExtJobId(mediaProduceJobPO.getVideoTaskJobId().toString());
            } else {
                callbackBizParamDTO.setExtJobId(mediaProduceJobPO.getExtJobId());
            }
            callbackBizParamDTO.setChannel(mediaProduceJobPO.getChannel());
            callbackBizParamDTO.setExtJobStatus(jobInfo.getJobStatus());
            callbackBizParamDTO.setJobResult(jobInfo.getJobResult());
            callbackBizParamDTO.setSynthStarted(jobInfo.getSynthStarted());
            callbackBizParamDTO.setSynthUpdated(jobInfo.getSynthUpdated());

            ResultModel callbackResult = jimuVideoProduceCallBackClient
                    .callback(mediaProduceJobPO.getCallbackUrl(), callbackBizParamDTO);

            CallbackLogPO callbackLog = initCallbackLog(ServiceChannelEnum.XHZY, JSONUtil.toJsonStr(jobInfo),
                    jobInfo.getJobId());
            callbackLog.setStatus(callbackResult.isSuccess() ? Const.ONE : Const.TWO);
            callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult) + "==mediaProduceStatusJobHandler");
            callbackLogManager.save(callbackLog);
        } catch (Exception e) {
            LOGGER.error("处理单条视频合成记录发生异常，mediaProduceJobPO:{},,,e:{}", JSONUtil.toJsonStr(mediaProduceJobPO), e);
        }
    }

    private CallbackLogPO initCallbackLog(ServiceChannelEnum e, String extCallbackRespBody, String recordId) {
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(recordId);
        callbackLog.setCallbackType(Const.ONE);
        callbackLog.setChannel(e.getCode());
        callbackLog.setExtCallbackRespBody(extCallbackRespBody);
        return callbackLog;
    }
}
