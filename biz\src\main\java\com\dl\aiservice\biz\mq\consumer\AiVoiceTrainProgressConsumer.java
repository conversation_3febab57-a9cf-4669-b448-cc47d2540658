package com.dl.aiservice.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.volcengine.VolcEngineVoiceClient;
import com.dl.aiservice.biz.client.volcengine.config.VolcEngineVoiceConfig;
import com.dl.aiservice.biz.client.volcengine.enums.VolcEngineVoiceTrainStatusEnum;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainStatusReq;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineVoiceTrainStatusResp;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.transaction.TransactionProxyManager;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.train.TrainResultManager;
import com.dl.aiservice.biz.manager.train.enums.TrainStatusEnum;
import com.dl.aiservice.biz.mq.dto.VoiceTrainProgressDTO;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import com.dl.aiservice.biz.mq.producer.AiVoiceTrainProducer;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 11:13
 */
@Component
public class AiVoiceTrainProgressConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(AiVoiceTrainProgressConsumer.class);

    @Resource
    private AiVoiceTrainProducer aiVoiceTrainProducer;

    @Resource
    private VolcEngineVoiceClient volcEngineVoiceClient;

    @Resource
    private VolcEngineVoiceConfig volcEngineVoiceConfig;

    @Resource
    private TrainJobManager trainJobManager;

    @Resource
    private TrainResultManager trainResultManager;

    @Resource
    private TransactionProxyManager transactionProxyManager;

    @Resource
    private CosFileUploadManager cosFileUploadManager;

    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    /**
     * 最大重试次数，120次
     * 120 * 10s = 1200s = 20min
     */
    private static final Integer MAX_RETRY_TIMES = 120;

    @StreamListener("voicetrainprogressconsumer")
    public void aiVoiceTrainProgressConsumer(@Payload VoiceTrainProgressDTO progressDTO) {
        LOGGER.info("aiVoiceTrainProgressConsumer开始消费,progressDTO:{}", JSONUtil.toJsonStr(progressDTO));

        //处理火山云的声音训练
        if (ServiceChannelEnum.VOLC_ENGINE.getCode().equals(progressDTO.getChannel())) {
            transactionProxyManager.process(() -> handleVolcEngine(progressDTO));
            return;
        }

        return;
    }

    private void handleVolcEngine(VoiceTrainProgressDTO progressDTO) {
        //1.查询已有训练任务的状态
        TrainJobPO trainJobPO = trainJobManager.getOne(Wrappers.lambdaQuery(TrainJobPO.class)
                .eq(TrainJobPO::getTrainJobId, progressDTO.getTrainJobId()));
        //幂等处理
        if (!TrainStatusEnum.MAKING.getCode().equals(trainJobPO.getStatus())) {
            LOGGER.warn("当前训练任务已非训练中的状态，不再处理！progressDTO:{},,,trainJobPO:{}", JSONUtil.toJsonStr(progressDTO),
                    JSONUtil.toJsonStr(trainJobPO));
            return;
        }

        //2.调用火山接口查询合成进度
        VolcEngineVoiceTrainStatusReq statusReq = new VolcEngineVoiceTrainStatusReq();
        statusReq.setAppId(volcEngineVoiceConfig.getAppId());
        statusReq.setSpeakerId(progressDTO.getExtModelCode());
        VolcEngineVoiceTrainStatusResp statusResp = volcEngineVoiceClient.voiceTrainStatus(statusReq);
        LOGGER.info("火山云接口返回：statusResp:{}", JSONUtil.toJsonStr(statusResp));
        if (!Const.ZERO.equals(statusResp.getBaseResp().getStatusCode())) {
            LOGGER.error("调用火山接口查询合成进度失败，progressDTO:{},statusReq:{}", JSONUtil.toJsonStr(progressDTO),
                    JSONUtil.toJsonStr(statusReq));
            //重新发送mq
            this.volcEngineResendMQ(progressDTO, statusResp);
            return;
        }

        //3.状态转换以及后续处理
        VolcEngineVoiceTrainStatusEnum statusEnum = VolcEngineVoiceTrainStatusEnum.parse(statusResp.getStatus());

        if (Objects.isNull(statusEnum)) {
            LOGGER.error("火山云声音状态有误，未对应上已有枚举!status:{},,,statusResp:{}", statusResp.getStatus(),
                    JSONUtil.toJsonStr(statusResp));
            //已达到最大重试次数，则失败处理
            this.volcEngineTrainFail(progressDTO, statusResp.getBaseResp().getStatusCode() + "",
                    "火山云声音状态:" + statusResp.getStatus() + "有误，未对应上已有枚举");
            return;
        }
        switch (statusEnum) {
        case SUCCESS:
            this.volcEngineTrainSuccess(progressDTO, statusResp);
            break;
        case TRAINING:
            this.volcEngineResendMQ(progressDTO, statusResp);
            break;
        case FAILED:
            this.volcEngineTrainFail(progressDTO, statusResp.getBaseResp().getStatusCode() + "",
                    statusResp.getBaseResp().getStatusMessage());
            break;
        case ACTIVE:
            //理论上不会走到这里。 先只打印日志。
            LOGGER.warn("当前声音已激活。progressDTO:{},,,statusResp:{}", JSONUtil.toJsonStr(progressDTO),
                    JSONUtil.toJsonStr(statusResp));
            break;
        default:
            //理论上不会走到这里。 先只打印日志。
            LOGGER.error("该状态未有对应举措。status:{},,,progressDTO:{},,,statusResp:{}", statusEnum.getStatus(),
                    JSONUtil.toJsonStr(progressDTO), JSONUtil.toJsonStr(statusResp));
        }

    }

    private void volcEngineResendMQ(VoiceTrainProgressDTO progressDTO, VolcEngineVoiceTrainStatusResp statusResp) {
        LOGGER.info("进入火山云声音训练重发mq流程。trainResultBizId:{},,,trainJobId:{},,,extModelCode:{},,,count:{}",
                progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(), progressDTO.getExtModelCode(),
                progressDTO.getCount());
        //未到最大重试次数，则重新发送消息
        if (progressDTO.getCount() < MAX_RETRY_TIMES) {
            progressDTO.setCount(progressDTO.getCount() + 1);
            aiVoiceTrainProducer.sendVoiceTrainProgressMQ(progressDTO, DelayLevelEnum.TEN_SECEND);
            return;
        }
        //已达到最大重试次数，则失败处理
        this.volcEngineTrainFail(progressDTO, statusResp.getBaseResp().getStatusCode() + "",
                "已达到最大重试次数。火山云statusMessage:" + statusResp.getBaseResp().getStatusMessage());
    }

    private void volcEngineTrainFail(VoiceTrainProgressDTO progressDTO, String failCode, String failReason) {
        LOGGER.info("进入火山云声音训练失败处理流程。trainResultBizId:{},,,trainJobId:{},,,extModelCode:{}",
                progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(), progressDTO.getExtModelCode());
        TrainResultPO trainResultPO = trainResultManager.getOne(Wrappers.lambdaQuery(TrainResultPO.class)
                .eq(TrainResultPO::getBizId, progressDTO.getTrainResultBizId()));

        trainResultManager.update(Wrappers.lambdaUpdate(TrainResultPO.class)
                .eq(TrainResultPO::getBizId, progressDTO.getTrainResultBizId())
                .set(TrainResultPO::getStatus, TrainStatusEnum.FAIL.getCode())
                .set(TrainResultPO::getTrainedNum, trainResultPO.getTrainedNum() - 1)
                .set(TrainResultPO::getLatestFailReason, failReason).set(TrainResultPO::getModifyDt, new Date()));

        trainJobManager.update(Wrappers.lambdaUpdate(TrainJobPO.class)
                .eq(TrainJobPO::getTrainJobId, progressDTO.getTrainJobId())
                .set(TrainJobPO::getStatus, TrainStatusEnum.FAIL.getCode()).set(TrainJobPO::getFailCode, failCode)
                .set(TrainJobPO::getFailReason, failReason).set(TrainJobPO::getModifyDt, new Date()));
        LOGGER.info("火山云声音训练失败处理流程结束！trainResultBizId:{},,,trainJobId:{},,,extModelCode:{},,,progressDTO:{}",
                progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(), progressDTO.getExtModelCode(),
                JSONUtil.toJsonStr(progressDTO));
    }

    private void volcEngineTrainSuccess(VoiceTrainProgressDTO progressDTO, VolcEngineVoiceTrainStatusResp statusResp) {
        LOGGER.info("进入火山云声音训练成功处理流程。trainResultBizId:{},,,trainJobId:{},,,extModelCode:{}",
                progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(), progressDTO.getExtModelCode());
        //1.将demoAudio下载到本地并上传到cos
        String sampleLink = "";
        File file = null;
        try {
            String obejetKey = progressDTO.getTrainJobId() + ".wav";
            String savePath = localPathPrefix + obejetKey;
            //文件下载
            file = DownloadUtil.downloadFile(statusResp.getDemoAudio(), savePath);
            sampleLink = cosFileUploadManager.uploadFile(file, null, null);
            LOGGER.info("火山云声音训练sampleLink:{},,,,trainJobId:{},,,extModelCode:{}", sampleLink,
                    progressDTO.getTrainJobId(), progressDTO.getExtModelCode());
        } catch (Exception e) {
            LOGGER.error(
                    "火山云demoLink 处理试听链接下载上传出现异常! demoAudio:{},,,trainResultBizId:{},,,trainJobId:{},,,extModelCode:{},,, e:{}",
                    statusResp.getDemoAudio(), progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(),
                    progressDTO.getExtModelCode(), e);
            //重新发送mq
            this.volcEngineResendMQ(progressDTO, statusResp);
            return;
        } finally {
            FileUtils.deleteQuietly(file);
        }

        //2.更新trainResult和trainJob
        trainResultManager.update(Wrappers.lambdaUpdate(TrainResultPO.class)
                .eq(TrainResultPO::getBizId, progressDTO.getTrainResultBizId())
                .set(TrainResultPO::getStatus, TrainStatusEnum.SUCCESS.getCode())
                .set(StringUtils.isNotBlank(sampleLink), TrainResultPO::getSampleLink, sampleLink)
                .set(TrainResultPO::getLatestFailReason, null).set(TrainResultPO::getModifyDt, new Date()));

        trainJobManager.update(Wrappers.lambdaUpdate(TrainJobPO.class)
                .eq(TrainJobPO::getTrainJobId, progressDTO.getTrainJobId())
                .set(TrainJobPO::getStatus, TrainStatusEnum.SUCCESS.getCode())
                .set(StringUtils.isNotBlank(sampleLink), TrainJobPO::getSampleLink, sampleLink)
                .set(TrainJobPO::getModifyDt, new Date()));
        LOGGER.info("火山云声音训练成功处理流程结束！trainResultBizId:{},,,trainJobId:{},,,extModelCode:{},,,progressDTO:{}",
                progressDTO.getTrainResultBizId(), progressDTO.getTrainJobId(), progressDTO.getExtModelCode(),
                JSONUtil.toJsonStr(progressDTO));

        /*//2.调用激活接口
        VolcEngineVoiceTrainActivateReq activateReq = new VolcEngineVoiceTrainActivateReq();
        activateReq.setAppId(volcEngineVoiceConfig.getAppId());
        activateReq.setSpeakerIds(Lists.newArrayList(progressDTO.getExtModelCode()));
        VolcEngineVoiceTrainActivateResp activateResp = volcEngineVoiceClient.
                voiceTrainActivate("ActivateMegaTTSTrainStatus", "2023-11-07", activateReq);
        if (Objects.nonNull(activateResp.getResult()) && CollectionUtils
                .isNotEmpty(activateResp.getResult().getStatuses())) {
            VolcEngineVoiceTrainActivateStatus activateRespResult = activateResp.getResult().getStatuses().get(0);
            //成功
            if ("Active".equals(activateRespResult.getState())) {
                //3.1 激活成功处理：更新trainResult和trainJob
                trainResultManager.update(Wrappers.lambdaUpdate(TrainResultPO.class)
                        .eq(TrainResultPO::getBizId, progressDTO.getTrainResultBizId())
                        .set(TrainResultPO::getStatus, TrainStatusEnum.SUCCESS.getCode())
                        .set(StringUtils.isNotBlank(sampleLink), TrainResultPO::getSampleLink, sampleLink)
                        .set(TrainResultPO::getLatestFailReason, null).set(TrainResultPO::getModifyDt, new Date()));

                trainJobManager.update(Wrappers.lambdaUpdate(TrainJobPO.class)
                        .eq(TrainJobPO::getTrainJobId, progressDTO.getTrainJobId())
                        .set(TrainJobPO::getStatus, TrainStatusEnum.SUCCESS.getCode())
                        .set(StringUtils.isNotBlank(sampleLink), TrainJobPO::getSampleLink, sampleLink)
                        .set(TrainJobPO::getModifyDt, new Date()));
                LOGGER.info("火山云声音激活成功！progressDTO:{}", JSONUtil.toJsonStr(progressDTO));
                return;
            }
        }
        //3.2 激活失败处理
        LOGGER.error("火山云声音激活失败！progressDTO:{},,,activateReq:{},,,activateResp:{}", JSONUtil.toJsonStr(progressDTO),
                JSONUtil.toJsonStr(activateReq), JSONUtil.toJsonStr(activateResp));
        this.volcEngineTrainFail(progressDTO, null, "声音训练完成但激活失败!");*/
    }

}
