package com.dl.aiservice.biz.client.huawei.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HuaweiTtsRequest {

    /**
     * 推理任务名称
     */
    private String name;
    /**
     * 训练id
     */
    @JsonProperty(value = "training_job_id")
    private String trainingJobId;
    /**
     * 推理文本
     */
    @JsonProperty(value = "user_input_text")
    private String userInputText;
    /**
     * 推理配置信息
     */
    private HuaweiTtsConfig config;

    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class HuaweiTtsConfig {

        /**
         * 语速50-200
         */
        private Integer speed;
        /**
         * 音高50-200
         */
        private Integer pitch;
        /**
         * 音量50-200
         */
        private Integer volume;
    }
}
