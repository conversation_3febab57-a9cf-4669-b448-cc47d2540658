package com.dl.aiservice.biz.service.digital.huawei.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum HuaweiSynthesisStatusEnum {


    Created(0, "未初始化"),
    Executing(1, "生成中"),
    Finished(2, "生成成功"),
    Failed(3, "生成失败"),
    ;


    private final Integer code;
    private final String desc;

    HuaweiSynthesisStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static HuaweiSynthesisStatusEnum getEnum(String desc) {
        Optional<HuaweiSynthesisStatusEnum> first =
                Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst();
        return first.orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
