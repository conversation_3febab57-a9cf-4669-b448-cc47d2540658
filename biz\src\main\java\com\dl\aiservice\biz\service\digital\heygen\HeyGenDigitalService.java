package com.dl.aiservice.biz.service.digital.heygen;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;

import java.io.File;

public interface HeyGenDigitalService extends BaseDigitalService {

    String getCosUrl(String taskId,String url,String fileType);

    CreateTrainingResponseDTO training(String phUrl,String name,Integer gender,String tenantCode);
}
