package com.dl.aiservice.share.videoproduce.dto.jimu;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class JimuDigitalManInfoDTO {

    /**
     * 数字人形象代码
     */
    String avatar;

    /**
     * 合成音代码
     */
    String per;

    /**
     * 是否开启字幕显示 true 、false
     */
    Boolean showSubtitle;
}
