package com.dl.aiservice.biz.client.jimu;

import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.client.jimu.dto.JobInfo;
import com.dl.aiservice.biz.client.jimu.dto.MediaAsrListInfo;
import com.dl.aiservice.biz.client.jimu.dto.MediaInfo;
import com.dl.aiservice.biz.client.jimu.dto.TemplateVarInfo;
import com.dl.aiservice.biz.client.jimu.intecepter.JimuProduceInterceptor;
import com.dl.aiservice.share.videoproduce.dto.jimu.TemplateVarDTO;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Header;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

import java.util.Map;

/**
 * @ClassName VoiceCallbackClient
 * @Description
 * <AUTHOR>
 * @Date 2023/3/16 17:43
 * @Version 1.0
 **/
@BaseRequest(baseURL = "http://api.shuwen.com/web/jimu", interceptor = JimuProduceInterceptor.class)
public interface JimuWebClient {

    String JIMU_PRODUCE_URL = "/external/template/produce";
    String HEADER_MEDIA_BIZ_ID = "M-bizId";
    String HEADER_TASK_JOB_ID = "T-jobId";
    String HEADER_TENANT_CODE = "T-code";

    /**
     * 积木视频合成请求
     *
     * @param headers
     * @param templateVar
     * @param bizCallbackUrl
     * @return
     */
    @Post(url = JIMU_PRODUCE_URL, dataType = "json")
    JimuResult<String> produce(@Header Map<String, Object> headers, @JSONBody TemplateVarDTO templateVar,
            String bizCallbackUrl);

    /**
     * 查询积木合成任务状态
     *
     * @param jobId
     * @return
     */
    @Get(url = "/external/play_job/detail/{jobId}")
    JimuResult<JobInfo> getVideoInfo(@Var("jobId") String jobId);

    /**
     * 获取积木模板变量
     *
     * @param templateId
     * @return
     */
    @Get(url = "/external/template/get_variables/{templateId}")
    JimuResult<TemplateVarInfo> getVariables(@Var("templateId") String templateId);

    /**
     * 上传媒体素材到积木侧
     *
     * @param mediaUrl
     * @param taskCode
     * @return
     */
    @Post(url = "/external/mediaInfo/add")
    JimuResult<MediaInfo> addMedia(@JSONBody("url") String mediaUrl, @JSONBody("taskCode") String taskCode);

    /**
     * 提取字幕
     *
     * @param mediaId
     * @return
     */
    @Get(url = "/external/mediaInfo/task/result/{mediaId}")
    JimuResult<MediaAsrListInfo> getAsrResult(@Var("mediaId") String mediaId);

    /**
     * 更新字幕
     *
     * @param mediaAsrInfo
     * @return
     */
    @Post(url = "/external/mediaInfo/asr/update")
    JimuResult<Boolean> asrUpdate(@JSONBody MediaAsrListInfo mediaAsrInfo);

}
