package com.dl.aiservice.biz.client.heygen;

import com.dl.aiservice.biz.client.heygen.interceptor.HeyGenDigitalTrainInterceptor;
import com.dl.aiservice.biz.client.heygen.req.HeyGenCreateVideoRequest;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenBaseResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalCreateVideoResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalVideoStatusResponse;
import com.dtflys.forest.annotation.*;

@BaseRequest(baseURL = "https://api.heygen.com/v1", interceptor = HeyGenDigitalTrainInterceptor.class)
public interface HeyGenDigitalClient {

    /**
     * 生成数字人视频
     * @param request
     * @return
     */
    @Post(url = "/video.generate", interceptor = HeyGenDigitalTrainInterceptor.class)
    HeyGenBaseResponse<HeyGenDigitalCreateVideoResponse> createVideo(@JSONBody HeyGenCreateVideoRequest request);

    /**
     * 查询视频合成状态
     * @param videoId
     * @return
     */
    @Get(url = "/video_status.get", interceptor = HeyGenDigitalTrainInterceptor.class)
    HeyGenBaseResponse<HeyGenDigitalVideoStatusResponse> getVideoStatus(@Query("video_id") String videoId);

}
