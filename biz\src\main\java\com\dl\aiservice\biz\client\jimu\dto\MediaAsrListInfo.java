package com.dl.aiservice.biz.client.jimu.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MediaAsrListInfo {

    String mediaId;

    Integer status;

    List<MediaAsrInfo> tagExtDTOList;

}
