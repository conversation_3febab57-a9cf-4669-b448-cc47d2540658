package com.dl.aiservice.biz.service.digital.huawei.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.huawei.HuaweiDigitalClient;
import com.dl.aiservice.biz.client.huawei.req.HuaweiTokenRequest;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.cos.ObsFileUploadManager;
import com.dl.aiservice.biz.mq.producer.AiDigitalManProducer;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ProgressRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.huawei.HuaweiDigitalService;
import com.dl.aiservice.biz.service.digital.huawei.enums.HuaweiSynthesisStatusEnum;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.http.ForestResponse;
import com.google.common.collect.Lists;
import com.huaweicloud.sdk.cbs.v1.CbsClient;
import com.huaweicloud.sdk.cbs.v1.model.CharacterConfig;
import com.huaweicloud.sdk.cbs.v1.model.CreateVideoReq;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteComposeVideoRequest;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteComposeVideoResponse;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteCreateVideoRequest;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteCreateVideoResponse;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteGetCharactersRequest;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteGetCharactersResponse;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteGetVideoInfoByIdRequest;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteGetVideoInfoByIdResponse;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteUpdateVideoInfoByIdRequest;
import com.huaweicloud.sdk.cbs.v1.model.ExecuteUpdateVideoInfoByIdResponse;
import com.huaweicloud.sdk.cbs.v1.model.PutVideoInfo;
import com.huaweicloud.sdk.cbs.v1.model.ReadConfig;
import com.huaweicloud.sdk.cbs.v1.model.TtsConfig;
import com.huaweicloud.sdk.cbs.v1.model.VideoConfig;
import com.huaweicloud.sdk.cbs.v1.region.CbsRegion;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
public class HuaweiDigitalServiceImpl extends AbstractDigitalService implements HuaweiDigitalService {

    @Resource
    private AiDigitalManProducer aiDigitalManProducer;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    private CbsClient client;
    @Resource
    private ObsFileUploadManager obsFileUploadManager;
    @Resource
    private HuaweiDigitalClient huaweiDigitalClient;

    @Value("${digtal.huawei.ak}")
    private String ak;
    @Value("${digtal.huawei.sk}")
    private String sk;

    //    private final String ak = "JFAM0LVHAXJ8V0GL3JML";

    //    private final String sk = "iD30R0ADRAfHzIrXkXv0tviwY2S3zX1WvHyKmB0E";

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.HUAWEI);
    }

    @PostConstruct
    private void createClient() {
        ICredential auth = new BasicCredentials().withAk(ak).withSk(sk);

        this.client = CbsClient.newBuilder().withCredential(auth).withRegion(CbsRegion.valueOf("cn-north-4")).build();
    }

    @Override
    public String getToken() {
        HuaweiTokenRequest request = HuaweiTokenRequest.builder()
                .auth(HuaweiTokenRequest.HuaweiAuthDTO.builder()
                        .scope(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiScopeDTO.builder()
                                .project(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiScopeDTO.HuaweiProjectDTO.builder()
                                        .name("cn-north-4")
                                        .build())
                                .build())
                        .identity(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiIdentityDTO.builder()
                                .hw_ak_sk(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiIdentityDTO.HwAkSkDTO.builder()
                                        .access(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiIdentityDTO.HwAkSkDTO.HuaweiAccessDTO.builder()
                                                .key(ak)
                                                .build())
                                        .secret(HuaweiTokenRequest.HuaweiAuthDTO.HuaweiIdentityDTO.HwAkSkDTO.HuaweiSecretDTO.builder()
                                                .key(sk)
                                                .build())
                                        .build())
                                .methods(ListUtil.of("hw_ak_sk"))
                                .build())
                        .build())
                .build();

        ForestResponse<String> token = huaweiDigitalClient.getToken(request);
        return token.getHeaderValue("X-Subject-Token");
    }


    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO requestDTO) {

        log.info("华为云视频合成请求参数：{}", JSONUtil.toJsonStr(requestDTO));
        ExecuteUpdateVideoInfoByIdRequest buildRequest = null;
        ExecuteUpdateVideoInfoByIdResponse buildResponse = null;
        try {
            ExecuteCreateVideoRequest request = new ExecuteCreateVideoRequest();
            CreateVideoReq body = new CreateVideoReq();
            body.setName(hostTimeIdg.generateId().toString());
            request.withBody(body);
            //1.创建视频
            ExecuteCreateVideoResponse response = client.executeCreateVideo(request);
            //2.配置视频
            buildRequest = getBuildRequest(requestDTO, response);
            log.info("配置视频请求参数：{}", JSONUtil.toJsonStr(buildRequest));
            buildResponse = client.executeUpdateVideoInfoById(buildRequest);
            log.info("配置视频返回结果：{}", JSONUtil.toJsonStr(buildResponse));
            //3.开始合成视频
            ExecuteComposeVideoRequest beginRequest = new ExecuteComposeVideoRequest();
            beginRequest.setVideoId(response.getId());
            ExecuteComposeVideoResponse beginResponse = client.executeComposeVideo(beginRequest);
            log.info("开始合成视频返回结果：{}", JSONUtil.toJsonStr(beginResponse));
            //4.保存日志
            saveLog(requestDTO, buildRequest, buildResponse, null);

            CreateResponseDTO createResponseDTO = new CreateResponseDTO();
            createResponseDTO.setTaskId(buildRequest.getVideoId());
            createResponseDTO.setMediaJobId(requestDTO.getUpdateId());
            createResponseDTO.setWorksBizId(requestDTO.getWorksBizId());
            //5.发送消息异步轮训查询结果
            ProgressRequestDTO progressRequestDTO = new ProgressRequestDTO();
            BeanUtils.copyProperties(createResponseDTO, progressRequestDTO);
            progressRequestDTO.setCount(0);
            aiDigitalManProducer.getProgressAndCallBackDelayed(progressRequestDTO);
            return createResponseDTO;

        } catch (Exception e) {
            log.info("HuaweiDigitalServiceImpl.videoCreate 异常，request={}", JsonUtils.toJSON(requestDTO));
            saveLog(requestDTO, buildRequest, buildResponse, e);
            log.error("HuaweiDigitalServiceImpl.videoCreate 异常", e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }

    private void saveLog(CreateRequestDTO paramDTO, ExecuteUpdateVideoInfoByIdRequest request, ExecuteUpdateVideoInfoByIdResponse response, Exception e) {
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setId(paramDTO.getUpdateId());
        job.setJobContent(JsonUtils.toJSON(request));
        if (Objects.nonNull(response)) {
            if (Objects.nonNull(response.getStatus())) {
                job.setExtJobId(request.getVideoId());
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(Const.ONE);
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                job.setResponseDt(new Date());
            }
        } else {
            job.setStatus(-Const.ONE);
            job.setFailCode("");
            job.setResponseDt(new Date());
            e.setStackTrace(new StackTraceElement[0]);
            job.setFailReason(JSONUtil.toJsonStr(e));
        }
        mediaProduceJobManager.updateById(job);
    }

    private ExecuteUpdateVideoInfoByIdRequest getBuildRequest(CreateRequestDTO requestDTO, ExecuteCreateVideoResponse response) {
        ExecuteUpdateVideoInfoByIdRequest buildRequest = new ExecuteUpdateVideoInfoByIdRequest();
        buildRequest.setVideoId(response.getId());

        PutVideoInfo buildBody = new PutVideoInfo();
        TtsConfig ttsConfig = new TtsConfig();
        ttsConfig.setProperty(requestDTO.getSpeakerId());
        ttsConfig.setVolume(StringUtils.isNotEmpty(requestDTO.getVolume()) ? Integer.parseInt(requestDTO.getVolume()) : 100);
        ttsConfig.setSpeed(StringUtils.isNotEmpty(requestDTO.getSpeechRate()) ? Integer.parseInt(requestDTO.getSpeechRate()) : 100);
        ttsConfig.setDelay((float) 0);
        buildBody.setTtsConfig(ttsConfig);

        ReadConfig readConfig = new ReadConfig();
        if (Objects.equals(requestDTO.getType(), Const.ONE)) {
            //文本驱动
            readConfig.setReadType(0);
            readConfig.setReadContent(requestDTO.getText());
        }
        if (Objects.equals(requestDTO.getType(), Const.ZERO)) {

            String audioUrl = requestDTO.getAudioUrl();
            byte[] file = HttpUtil.createGet(audioUrl).execute().bodyBytes();
            String filePath = hostTimeIdg.generateId().toString() + "." + audioUrl.substring(audioUrl.lastIndexOf('.') + 1);
            File audioFile;
            audioFile = new File(filePath);
            try {
                FileUtils.writeByteArrayToFile(new File(filePath), file, false);
                String url = obsFileUploadManager.uploadFile(audioFile, null, null);
                FileUtil.del(audioFile.getAbsolutePath());
                //音频驱动
                readConfig.setReadType(3);
                readConfig.setAudioUrl(url);
            } catch (IOException e) {
                log.error("华为云数字人文件写入失败", e);
                throw BusinessServiceException.getInstance("华为云数字人文件写入失败");
            }
        }
        buildBody.setReadConfig(readConfig);
        VideoConfig videoConfig = new VideoConfig();
        //绿幕
        videoConfig.setBackgroundId("6cb0f37a-3f37-47a7-bada-4c83b664caac");
        //透明
//        videoConfig.setBackgroundId("87e8fe97-bb2d-4725-af5e-3d760735f0cb");
//            videoConfig.setResolutionType(1);
        buildBody.setVideoConfig(videoConfig);

        CharacterConfig characterConfig = new CharacterConfig();
        characterConfig.setCharacterId(requestDTO.getSceneId());
        buildBody.setCharacterConfig(characterConfig);

        buildRequest.withBody(buildBody);
        return buildRequest;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {

        ExecuteGetCharactersRequest request = new ExecuteGetCharactersRequest();
        try {
            ExecuteGetCharactersResponse response = client.executeGetCharacters(request);
            List<RobotResponseDTO> collect = response.getCharacters().stream().map(character -> {
                RobotResponseDTO robotResponseDTO = new RobotResponseDTO();
                robotResponseDTO.setId(character.getId());
                robotResponseDTO.setRobotName(character.getName());
                robotResponseDTO.setType(character.getType());
                robotResponseDTO.setCoverUrl(character.getPhotoUrl());
                return robotResponseDTO;
            }).collect(Collectors.toList());
            ResultPageModel<RobotResponseDTO> pageResponseDTO = new ResultPageModel<>();
            pageResponseDTO.setDataResult(collect);
            return pageResponseDTO;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("获取华为机器人列表失败：{}", e.getMessage());
        }
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
//
//        ExecuteGetVideosListRequest request = new ExecuteGetVideosListRequest();
//        request.setName("1");
//        request.setLimit(100);
//        request.setOffset(0);
//        ExecuteGetVideosListResponse response = client.executeGetVideosList(request);
//        response.getVideos().forEach(video -> {
//            ExecuteDeleteVideoByIdRequest request2 = new ExecuteDeleteVideoByIdRequest();
//            request2.setVideoId(video.getId());
//            client.executeDeleteVideoById(request2);
//        });
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO trainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {

        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
//                .eq(MediaProduceJobPO::getStatus, Const.ZERO)
                .eq(MediaProduceJobPO::getExtJobId, taskRequestDTO.getTaskId())
                .eq(MediaProduceJobPO::getWorksBizId, taskRequestDTO.getWorksBizId()));

        if (Objects.nonNull(mediaProduceJobPO) && Objects.equals(mediaProduceJobPO.getStatus(), Const.ZERO)) {
            ProgressResponseDTO progressResponseDTO = new ProgressResponseDTO();
            progressResponseDTO.setWorksBizId(taskRequestDTO.getWorksBizId());
            progressResponseDTO.setSynthesisStatus(mediaProduceJobPO.getStatus());
            progressResponseDTO.setVideoUrl(mediaProduceJobPO.getMediaUrl());
            progressResponseDTO.setDuration(mediaProduceJobPO.getDuration().toString());
            return progressResponseDTO;
        }

        ProgressResponseDTO respDto = new ProgressResponseDTO();

        ExecuteGetVideoInfoByIdRequest request = new ExecuteGetVideoInfoByIdRequest();
        request.setVideoId(taskRequestDTO.getTaskId());
        try {
            ExecuteGetVideoInfoByIdResponse resp = client.executeGetVideoInfoById(request);
            log.info("HuaweiDigitalServiceImpl.getProgress: request={},response={}", JsonUtils.toJSON(request), JsonUtils.toJSON(resp));

            if (Objects.equals(HuaweiSynthesisStatusEnum.Finished.getCode(), resp.getStatus())) {
                String url = buildFileUrl(resp, taskRequestDTO.getTaskId());
                respDto.setSynthesisStatus(SynthesisStatusEnum.SUCCESS.getCode());
                respDto.setVideoUrl(url);
                //                respDto.setDuration(videoAiResult.getOutputDuration());
            } else {
                respDto.setSynthesisStatus(SynthesisStatusEnum.MAKING.getCode());
            }
            return respDto;
        } catch (Exception e) {
            log.info("HuaweiDigitalServiceImpl.getProgress 异常，request={}", JsonUtils.toJSON(request));
            log.error("HuaweiDigitalServiceImpl.getProgress 异常", e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }

    public String buildFileUrl(ExecuteGetVideoInfoByIdResponse responseData, String taskId) {
        String video = responseData.getVideoUrl();

        byte[] file = HttpUtil.createGet(video).execute().bodyBytes();
        String filePath = taskId + "." + video.substring(video.lastIndexOf('.') + 1, video.lastIndexOf('?'));
        File audioFile;
        String url;
        try {
            audioFile = new File(filePath);
            FileUtils.writeByteArrayToFile(new File(filePath), file, false);
            url = cosFileUploadManager.uploadFile(audioFile, null, "/temp/visual/dm");
            FileUtil.del(audioFile.getAbsolutePath());
            MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
                    .eq(MediaProduceJobPO::getExtJobId, taskId));
            mediaProduceJobPO.setMediaUrl(url);
            mediaProduceJobPO.setStatus(Const.ZERO);
            mediaProduceJobPO.setResponseDt(new Date());
            mediaProduceJobManager.updateById(mediaProduceJobPO);
        } catch (IOException e) {
            log.error("华为云数字人文件写入失败", e);
            throw BusinessServiceException.getInstance("华为云数字人文件写入失败");
        }
        return url;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

}

