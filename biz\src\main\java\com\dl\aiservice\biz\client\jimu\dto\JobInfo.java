package com.dl.aiservice.biz.client.jimu.dto;

import com.dl.aiservice.share.videoproduce.dto.jimu.JobResultDTO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JobInfo {

    String jobId;
    /**
     * 合成视频名称
     */
    String playTitle;
    /**
     * 视频状态
     * Initial(0, "作业初始化"),
     * Build_Success(1, "构建成功"),
     * Build_Fail(11, "构建失败"),
     * Trans_Success(2, "协议转换成功"),
     * Trans_Fail(21, "协议转换失败"),
     * Synthesis_Create(3, "发起合成任务"),
     * Synthesis_Success(4, "合成任务成功"),
     * Synthesis_Fail(41, "合成任务失败"),
     */
    Integer jobStatus;
    String belongUserId;
    String triggerUserId;
    /**
     * 业务⾃定义标示, jobType 为 0 时, bizId 是模板 ID jobType 为 1 时, bizId 是任务 ID
     */
    String bizId;
    String tenantId;
    String id;
    /**
     * 任务执⾏类型: 0 - 单视频任务;1 - 批量 视频任务
     */
    Integer jobType;
    Long gmtModified;
    Long gmtCreate;
    /**
     * 合成发起时间
     */
    Long synthStarted;
    /**
     * 合成完成时间
     */
    Long synthUpdated;

    JobResultDTO jobResult;

}
