package com.dl.aiservice.biz.service.videomake.enums;

/**
 * @ClassName VideoMakeJobStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/6/29 14:29
 * @Version 1.0
 **/
public enum VideoMakeJobErrStatusEnum {

    CODE_49(49, "参数错误导致视频合成失败"),
    CODE_51(51, "同源策略视频合成供应商调用失败"),
    CODE_52(52, "视频合成失败，第三方声音供应商tts异常"),
    CODE_53(53, "照片版外部声音驱动视频合成失败"),
    CODE_54(54, "第三方数字分身视频合成异常导致视频合成失败"),
    CODE_55(55, "第三方数字分身合成全部成功，回调视频合成商合成失败"),
    CODE_56(56, "第三方数字分身部分合成失败");

    private Integer code;
    private String desc;

    VideoMakeJobErrStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
