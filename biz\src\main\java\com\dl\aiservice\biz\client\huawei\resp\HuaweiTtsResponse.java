package com.dl.aiservice.biz.client.huawei.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class HuaweiTtsResponse {


    /**
     * 返回创建推理任务信息
     */
    private HuaweiTtsJob job;

    @Data
    @Builder
    public static class HuaweiTtsJob {

        /**
         * 推理结果路径
         */
        @JsonProperty(value = "clone_audio_result")
        private String cloneAudioResult;
        /**
         * 创建时间
         */
        @JsonProperty(value = "created_time")
        private String createdTime;
        /**
         *
         */
        private String id;
        /**
         * 推理状态
         */
        @JsonProperty(value = "inference_status")
        private String inferenceStatus;
        /**
         * 推理任务名称
         */
        private String name;
        /**
         * 更新时间
         */
        @JsonProperty(value = "updated_time")
        private String updatedTime;
        /**
         * 用户输入的信息
         */
        @JsonProperty(value = "user_input_text")
        private String userInputText;
        /**
         * 返回的错误信息
         */
        @JsonProperty(value = "error_message")
        private String errorMessage;
    }
}
