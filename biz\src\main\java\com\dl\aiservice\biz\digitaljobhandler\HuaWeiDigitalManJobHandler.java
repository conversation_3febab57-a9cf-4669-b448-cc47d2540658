package com.dl.aiservice.biz.digitaljobhandler;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 华为云数字人任务处理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-12 16:27
 */
@Component
public class HuaWeiDigitalManJobHandler extends AbstractHaveConcurrencyLimitDigitalManJobHandler {

    /**
     * 任务队列的key
     */
    private static final String HUAWEI_DM_JOB_QUEUE_KEY = "Huawei_DM_Job_Queue";

    /**
     * 分布式锁的key
     */
    private static final String HUAWEI_DM_JOB_HANDLER_LOCK_KEY = "Huawei_Dm_Job_Handler_Lock";

    /**
     * 分布式锁的失效时间 单位：秒
     */
    private static final long HUAWEI_DM_JOB_HANDLER_LOCK_TIMEOUT = 4;

    @Value("${digtal.huawei.concurrency}")
    private Integer huaweiConcurrency;

    @Override
    public ServiceChannelEnum supportChannel() {
        return ServiceChannelEnum.HUAWEI;
    }

    @Override
    public String getQueueKey() {
        return HUAWEI_DM_JOB_QUEUE_KEY;
    }

    @Override
    String getHandlerLockKey() {
        return HUAWEI_DM_JOB_HANDLER_LOCK_KEY;
    }

    @Override
    long getHandlerLockTimeout() {
        return HUAWEI_DM_JOB_HANDLER_LOCK_TIMEOUT;
    }

    @Override
    int getConcurrency() {
        return huaweiConcurrency;
    }

}
