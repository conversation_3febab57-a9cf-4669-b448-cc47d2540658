package com.dl.aiservice.biz.client.heygen.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
@Data
public class HeyGenDigitalVideoStatusResponse implements Serializable {

    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "error")
    private HeyGenDigitalVideoStatusErrorResponse error;

    @JsonProperty(value = "callback_id")
    private String callBackId;

    @JsonProperty(value = "video_url")
    private String videoUrl;
}
