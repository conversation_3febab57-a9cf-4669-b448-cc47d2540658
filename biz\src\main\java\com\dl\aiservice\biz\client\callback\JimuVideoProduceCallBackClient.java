package com.dl.aiservice.biz.client.callback;

import com.dl.aiservice.biz.client.callback.intecepter.JimuProduceCallBackIntecepter;
import com.dl.aiservice.share.videoproduce.dto.VideoProduceCallbackBizParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

@BaseRequest
public interface JimuVideoProduceCallBackClient {
    @Post(value = "{callbackUrl}", interceptor = JimuProduceCallBackIntecepter.class)
    ResultModel callback(@Var("callbackUrl") String callbackUrl,
            @JSONBody VideoProduceCallbackBizParamDTO callbackBizParamDTO);
}
