package com.dl.aiservice.biz.service.digital.heygen.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.guiji.convert.PageConvertService;
import com.dl.aiservice.biz.client.heygen.HeyGenDigitalClient;
import com.dl.aiservice.biz.client.heygen.HeyGenDigitalTrainClient;
import com.dl.aiservice.biz.client.heygen.enums.HeyGenVideoStatusEnum;
import com.dl.aiservice.biz.client.heygen.req.HeyGenCreateVideoClipOffsetRequest;
import com.dl.aiservice.biz.client.heygen.req.HeyGenCreateVideoClipRequest;
import com.dl.aiservice.biz.client.heygen.req.HeyGenCreateVideoRequest;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenBaseResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalCreateVideoResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalTrainResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalVideoStatusResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManScenesManager;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.VideoStyleDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhAnchorDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.DigitalComposeEnum;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.heygen.HeyGenDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.DigitalManChannelEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
@Service
@Slf4j
public class HeyGenDigitalServiceImpl extends AbstractDigitalService implements HeyGenDigitalService,
        PageConvertService {

    private static final String RATIO = "9:16";

    @Autowired
    private HeyGenDigitalClient heyGenDigitalClient;

    @Autowired
    private DaVirtualManScenesManager daVirtualManScenesManager;

    @Autowired
    private DaVirtualManManager daVirtualManManager;

    @Autowired
    private HeyGenDigitalTrainClient heyGenDigitalTrainClient;

    @Autowired
    private AiConfig aiConfig;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private CosFileUploadManager cosFileUploadManager;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.HEYGEN);
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO request) {
        Assert.notNull(request.getType(), "合成类型不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(request.getSceneId()), "场景id不能为空");

        //查询数字人场景
        LambdaQueryWrapper<DaVirtualManScenesPO> sceneManWrapper = Wrappers.lambdaQuery(DaVirtualManScenesPO.class);
        sceneManWrapper.eq(DaVirtualManScenesPO::getSceneId, request.getSceneId())
                .eq(DaVirtualManScenesPO::getIsEnabled, Const.ONE).eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO);
        List<DaVirtualManScenesPO> daVirtualManScenesPOList = daVirtualManScenesManager.list(sceneManWrapper);
        Assert.isTrue(CollectionUtils.isNotEmpty(daVirtualManScenesPOList), "未找到数字人场景");
        DaVirtualManScenesPO daVirtualManScene = daVirtualManScenesPOList.get(Const.ZERO);

        //查询数字人
        DaVirtualManPO daVirtualMan = daVirtualManManager.getOne(Wrappers.lambdaQuery(DaVirtualManPO.class)
                .eq(DaVirtualManPO::getBizId, daVirtualManScene.getVmBizId())
                .eq(DaVirtualManPO::getIsEnabled, Const.ONE).eq(DaVirtualManPO::getIsDeleted, Const.ZERO));
        Assert.isTrue(Objects.nonNull(daVirtualMan), "未查询到数字人，请确认数字人是否存在，或是否启用状态");

        HeyGenCreateVideoRequest heyGenCreateVideoRequest = new HeyGenCreateVideoRequest();
        heyGenCreateVideoRequest.setBackground(request.getBackgroundUrl());
        heyGenCreateVideoRequest.setCaptionOpen(request.getShowSubtitles());
        heyGenCreateVideoRequest.setRatio(RATIO);
        heyGenCreateVideoRequest.setTest(aiConfig.getHeyGenTest());
        heyGenCreateVideoRequest.setVersion("v1alpha");
        List<HeyGenCreateVideoClipRequest> clips = new ArrayList<>();
        heyGenCreateVideoRequest.setClips(clips);
        HeyGenCreateVideoClipRequest heyGenCreateVideoClipRequest = new HeyGenCreateVideoClipRequest();
        clips.add(heyGenCreateVideoClipRequest);
        //区分到底传TalkingPhotoId还是AvatarId
        if (Const.ONE.equals(daVirtualMan.getVmType())){
            heyGenCreateVideoClipRequest.setTalkingPhotoId(daVirtualManScene.getSceneId());
            heyGenCreateVideoClipRequest.setTalkingPhotoStyle("normal");
        }else if (Const.TWO.equals(daVirtualMan.getVmType())){
            heyGenCreateVideoClipRequest.setAvatarId(daVirtualManScene.getSceneId());
        }

        if (Objects.nonNull(request.getAnchorParam())){
            IvhAnchorDTO anchorDTO = request.getAnchorParam();
            heyGenCreateVideoClipRequest.setScale(anchorDTO.getScale());
        }

        //处理数字人位置
        if (Objects.nonNull(request.getStyle())){
            VideoStyleDTO styleDTO = request.getStyle();
            HeyGenCreateVideoClipOffsetRequest offset = new HeyGenCreateVideoClipOffsetRequest();
            offset.setX(Double.valueOf(styleDTO.getX()));
            offset.setY(Double.valueOf(styleDTO.getY()));
            heyGenCreateVideoClipRequest.setOffset(offset);
        }

        //文本驱动数字人
        if (Objects.equals(request.getType(), DigitalComposeEnum.TEXT.getCode())) {
            heyGenCreateVideoClipRequest.setInputText(request.getText());
            heyGenCreateVideoClipRequest.setVoiceId(request.getSpeakerId());

        }

        //音频驱动数字人
        if (Objects.equals(request.getType(), DigitalComposeEnum.VIDEO.getCode())) {
            Assert.isTrue(StringUtils.isNotBlank(request.getAudioUrl()),"音频地址不能为空");
            heyGenCreateVideoClipRequest.setInputAudio(request.getAudioUrl());
        }
        log.info("请求HeyGen合成数字人入参heyGenCreateVideoRequest = " + JSONUtil.toJsonStr(heyGenCreateVideoClipRequest));
        //生成数字人视频
        HeyGenBaseResponse<HeyGenDigitalCreateVideoResponse> resp = heyGenDigitalClient.createVideo(
                heyGenCreateVideoRequest);
        log.info("合成HeyGen数字人响应HeyGenBaseResponse = " + JSONUtil.toJsonStr(resp));
        CreateResponseDTO respDto = new CreateResponseDTO();
        if (!resp.isSuccess()){
            return null;
        }
        respDto.setTaskId(resp.getData().getVideoId());
        return respDto;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        log.info("heygen查询数字人合成状态入参 taskRequestDTO = " + JSONUtil.toJsonStr(taskRequestDTO));
        HeyGenBaseResponse<HeyGenDigitalVideoStatusResponse> heyGenResponse = heyGenDigitalClient.getVideoStatus(
                taskRequestDTO.getTaskId());
        log.info("heygen查询数字人合成状态返回状态 heyGenResponse = " + JSONUtil.toJsonStr(heyGenResponse));
        ProgressResponseDTO responseDTO = new ProgressResponseDTO();
        if (heyGenResponse.isSuccess()){
            if (Objects.isNull(heyGenResponse.getData())){
                responseDTO.setSynthesisStatus(SynthesisStatusEnum.MAKING.getCode());
                return responseDTO;
            }
            if (HeyGenVideoStatusEnum.COMPLETED.getStatus().equals(heyGenResponse.getData().getStatus())){
                responseDTO.setSynthesisStatus(SynthesisStatusEnum.SUCCESS.getCode());
                responseDTO.setVideoUrl(heyGenResponse.getData().getVideoUrl());
            }
            else if (HeyGenVideoStatusEnum.PROCESSING.getStatus().equals(heyGenResponse.getData().getStatus())){
                responseDTO.setSynthesisStatus(SynthesisStatusEnum.MAKING.getCode());
            }
            else if (HeyGenVideoStatusEnum.FAILED.getStatus().equals(heyGenResponse.getData().getStatus())){
                responseDTO.setSynthesisStatus(SynthesisStatusEnum.FAIL.getCode());
            }
            return responseDTO;
        }
        return null;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
      /*  File fileData = this.getFileData(request.getVideoUrl());
        try {
            HeyGenBaseResponse<HeyGenDigitalTrainResponse> response = heyGenDigitalTrainClient.trainUpload(
                    fileData);
            if (response.isSuccess()){
                CreateTrainingResponseDTO responseDTO = new CreateTrainingResponseDTO();
                responseDTO.setTaskId(response.getData().getTalkingPhotoId());
                return responseDTO;
            }
        }catch (Exception e){
            throw BusinessServiceException.getInstance("上传数字人照片失败");
        }*/
        return null;
    }

    @Override
    @Transactional
    public CreateTrainingResponseDTO training(String phUrl,String name,Integer gender,String tenantCode) {
        byte[] data = this.getFileData(phUrl);
        String coverUrl = getTrainCoverUrl(data, hostTimeIdg.generateId().toString(), "jpeg");
        HttpURLConnection connection = null;
        try {
            URL url = new URL("https://upload.heygen.com/v1/talking_photo");
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("X-Api-Key", aiConfig.getHeyGenApiKey());
            connection.setRequestProperty("Content-Type", "text/plain");
            connection.setDoOutput(true);

            try (DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream())) {
                dataOutputStream.write(data);
                // No need to flush or close the dataOutputStream as it's done automatically by try-with-resources.
            }

            int responseCode = connection.getResponseCode();
            BufferedReader reader;

            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }

            StringBuilder response = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            String json = response.toString();
            HeyGenBaseResponse heyGenBaseResponse = JSONUtil.toBean(json, HeyGenBaseResponse.class);
            if (heyGenBaseResponse.isSuccess()){
                String responseData = heyGenBaseResponse.getData().toString();
                HeyGenDigitalTrainResponse genDigitalTrainResponse = JSONUtil.toBean(responseData,
                        HeyGenDigitalTrainResponse.class);
                this.fillVirtualMan(genDigitalTrainResponse,name,gender,tenantCode,coverUrl);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return null;
    }

    private void fillVirtualMan(HeyGenDigitalTrainResponse genDigitalTrainResponse,String name,Integer gender,String tenantCode,String headImg){
        //保存 DaVirtualManPO
        DaVirtualManPO daVirtualManPO = new DaVirtualManPO();
        daVirtualManPO.setBizId(hostTimeIdg.generateId().longValue());
        daVirtualManPO.setChannel(DigitalManChannelEnum.HeyGen.getCode());
        daVirtualManPO.setVmCode(genDigitalTrainResponse.getTalkingPhotoId());
        daVirtualManPO.setIsDeleted(Const.ZERO);
        daVirtualManPO.setVmType(Const.ONE);
        daVirtualManPO.setVmName(name);
        daVirtualManPO.setGender(gender);
        daVirtualManPO.setIsEnabled(Const.ONE);
        daVirtualManPO.setHeadImg(headImg);
        daVirtualManManager.save(daVirtualManPO);

        DaVirtualManScenesPO daVirtualManScenesPO = new DaVirtualManScenesPO();
        daVirtualManScenesPO.setVmBizId(daVirtualManPO.getBizId());
        daVirtualManScenesPO.setCoverUrl(headImg);
        daVirtualManScenesPO.setSceneId(genDigitalTrainResponse.getTalkingPhotoId());
        daVirtualManScenesPO.setSceneName(name);
        daVirtualManScenesPO.setCloth(Const.ZERO);
        daVirtualManScenesPO.setPose(Const.ONE);
        daVirtualManScenesPO.setResolution(Const.ONE);
        daVirtualManScenesPO.setIsDeleted(Const.ZERO);
        daVirtualManScenesManager.save(daVirtualManScenesPO);
    }

    @Override
    public String getCosUrl(String taskId,String url,String fileType) {
        String filePath = taskId + "." + fileType;
        String cosUrl = null;
        File file = new File(filePath);
        byte[] fileContent = null;
        try {
            fileContent = DownloadUtil.getData(url);
            FileUtils.writeByteArrayToFile(file, fileContent, false);
            cosUrl = cosFileUploadManager.uploadFile(file, null, "/temp/visual/dm");
            FileUtil.del(file.getAbsolutePath());
            return cosUrl;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private byte[] getFileData(String url){
        String fileId = hostTimeIdg.generateId().toString();
        String filePath = fileId + "." + "jpeg";
        File file = new File(filePath);
        byte[] fileContent = null;
        fileContent = DownloadUtil.getData(url);
        return fileContent;
    }

    private String getTrainCoverUrl(byte[] data,String fileName,String type){
        String filePath = fileName + "." + type;
        String cosUrl = null;
        File file = new File(filePath);
        try {
            FileUtils.writeByteArrayToFile(file, data, false);
            cosUrl = cosFileUploadManager.uploadFile(file, null, "/temp/visual/dm");
            FileUtil.del(file.getAbsolutePath());
            return cosUrl;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
