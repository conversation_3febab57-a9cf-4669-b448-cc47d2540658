package com.dl.aiservice.biz.client.heygen.interceptor;

import com.dl.aiservice.biz.config.AiConfig;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HeyGenDigitalTrainInterceptor implements Interceptor {

    final private static String ACCEPT = "accept";

    final private static String CONTENT_TYPE = "content-type";

    final private static String API_KEY = "X-API-KEY";

    @Autowired
    private AiConfig aiConfig;


    @Override
    public boolean beforeExecute(ForestRequest request) {
        request.addHeader(ACCEPT,"application/json");
        request.addHeader(CONTENT_TYPE, "application/json");
        request.addHeader(API_KEY, aiConfig.getHeyGenApiKey());
        return true;
    }

}
