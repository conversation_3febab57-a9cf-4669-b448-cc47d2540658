package com.dl.aiservice.biz.client.heygen.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HeyGenCreateVideoClipRequest implements Serializable {

    /**
     * 头像的唯一标识符。avatar_id和talking_photo_id二选一。
     */
    @JsonProperty(value = "avatar_id")
    private String avatarId;

    /**
     * 数字人风格，在avatar_id存在时必填
     * - normal
     * - circle
     */
    @JsonProperty(value = "avatar_style")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String avatarStyle;

    /**
     *	训练的数字人标识。avatar_id和talking_photo_id二选一。
     */
    @JsonProperty(value = "talking_photo_id")
    private String talkingPhotoId;

    /**
     * 训练的数字人说话风格风格应该是
     * - normal
     * - circle
     */
    @JsonProperty(value = "talking_photo_style")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String talkingPhotoStyle;

    /**
     * avatar_id/talking_photo说出的文本内容。。input_text和input_audio二选一
     */
    @JsonProperty(value = "input_text")
    private String inputText;

    /**
     * 声音id，在inputText不为空时必填
     */
    @JsonProperty(value = "voice_id")
    private String voiceId;

    /**
     * 音频链接地址。input_text和input_audio二选一
     */
    @JsonProperty(value = "input_audio")
    private String inputAudio;

    /**
     * 数字人的比例，
     * 是指将数字人缩放到整个屏幕。
     * 表示将数字人缩放到屏幕的一半1 0.5
     */
    @JsonProperty(value = "scale")
    private Double scale = 1.0;

    /**
     * 数字人位置
     */
    @JsonProperty(value = "offset")
    private HeyGenCreateVideoClipOffsetRequest offset;
}
