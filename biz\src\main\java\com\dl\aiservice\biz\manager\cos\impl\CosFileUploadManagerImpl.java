package com.dl.aiservice.biz.manager.cos.impl;

import cn.hutool.core.io.FileUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.properties.cos.ApiProperties;
import com.dl.aiservice.biz.properties.cos.CosProperties;
import com.dl.aiservice.biz.properties.cos.TencentCloudProperties;
import com.dl.framework.common.idg.HostTimeIdg;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName CosFileUploadManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:58
 * @Version 1.0
 **/
@Slf4j
@Service
public class CosFileUploadManagerImpl implements CosFileUploadManager {

    private final static String FILE_DIR_TEMPLATE = "/temp/visual/tts/";
    private final static String FILE_URL = "https://%s.cos.%s.myqcloud.com/%s";

    @Autowired
    private TencentCloudProperties tencentCloudProperties;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Override
    public String uploadFile(File file, String type, String path) {
        if (Objects.isNull(file)) {
            log.error("腾讯云文件上传，入参file对象为空，不处理！");
            return null;
        }
        CosProperties cosProperties = tencentCloudProperties.getCosProperties();
        String bucketId = cosProperties.getBucketId();
        String fileName = file.getName();
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(FileUtil.extName(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + type;
        }
        if (StringUtils.hasLength(path)) {
            fileName = path + Const.SLASH + fileName;
        } else {
            fileName = FILE_DIR_TEMPLATE + fileName;
        }
        COSClient cosClient = createCli();
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketId, fileName, file);
            putObjectRequest.withMetadata(objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.warn("cos upload requestId={}", putObjectResult.getRequestId());
            return String.format(FILE_URL, cosProperties.getBucketId(), cosProperties.getRegion(), fileName);
        } catch (Exception e) {
            log.error("上传腾讯云存储桶时发生异常!fileName:{}", fileName, e);
        } finally {
            cosClient.shutdown();
        }
        return null;
    }

    @Override
    public String generatePresignedUrl(String filePath, HttpMethodName httpMethod) {
        // 调用 COS 接口之前必须保证本进程存在一个 COSClient 实例，如果没有则创建
        // 详细代码参见本页：简单操作 -> 创建 COSClient
        COSClient cosClient = createCli();
        try {
            // 存储桶的命名格式为 BucketName-APPID，此处填写的存储桶名称必须为此格式
            String bucketName = tencentCloudProperties.getCosProperties().getIvhBucketId();
            // 对象键(Key)是对象在存储桶中的唯一标识。详情请参见 [对象键](https://cloud.tencent.com/document/product/436/13324)
            String key = filePath;

            // 设置签名过期时间(可选), 若未进行设置则默认使用 ClientConfig 中的签名过期时间(1小时)
            // 这里设置签名在半个小时后过期
            Date expirationDate = new Date(System.currentTimeMillis() + 30 * 60 * 1000);

            // 填写本次请求的参数，需与实际请求相同，能够防止用户篡改此签名的 HTTP 请求的参数
            Map<String, String> params = new HashMap<>();

            // 填写本次请求的头部，需与实际请求相同，能够防止用户篡改此签名的 HTTP 请求的头部
            Map<String, String> headers = new HashMap<>();

            URL url = cosClient.generatePresignedUrl(bucketName, key, expirationDate, httpMethod, headers, params);
            return url.toString();
        } catch (Exception e) {
            log.error("COS预签名地址生成失败", e);
            return null;
        } finally {
            // 确认本进程不再使用 cosClient 实例之后，关闭即可
            cosClient.shutdown();
        }
    }

    private COSClient createCli() {
        ApiProperties api = tencentCloudProperties.getApi();
        // 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(api.getSecretId(), api.getSecretKey());
        // 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(tencentCloudProperties.getCosProperties().getRegion()));
        // 生成cos客户端
        return new COSClient(cred, clientConfig);
    }
}
