package com.dl.aiservice.biz.client.heygen;

import com.dl.aiservice.biz.client.heygen.interceptor.HeyGenDigitalTrainInterceptor;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenBaseResponse;
import com.dl.aiservice.biz.client.heygen.resp.HeyGenDigitalTrainResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.DataFile;
import com.dtflys.forest.annotation.Post;

import java.io.File;

@BaseRequest(baseURL = "https://upload.heygen.com/v1", interceptor = HeyGenDigitalTrainInterceptor.class)
public interface HeyGenDigitalTrainClient {

    String TRAIN_PATH = "/talking_photo";

    @Post(url = "/talking_photo", interceptor = HeyGenDigitalTrainInterceptor.class)
    HeyGenBaseResponse<HeyGenDigitalTrainResponse> trainUpload(@DataFile(value = "1.jpg",fileName = "1.jpg") byte[] data);
}
