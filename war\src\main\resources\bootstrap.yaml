spring:
  application:
    name: dl-aiservice
  cloud:
    compatibility-verifier:
      enabled: false
logging:
  level:
    com:
      alibaba:
        cloud:
          nacos:
            client: debug
ribbon:
  nacos:
    enabled: true
---
spring:
  profiles: test
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: 101.35.83.149:8848
      config:
        file-extension: yaml
        server-addr: 101.35.83.149:8848
        refresh-enabled: true

---
spring:
  profiles: dev
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: 101.35.83.149:8848
      config:
        file-extension: yaml
        server-addr: 101.35.83.149:8848
        refresh-enabled: true

---
spring:
  profiles: stable
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: 101.35.83.149:8848
      config:
        file-extension: yaml
        server-addr: 101.35.83.149:8848
        refresh-enabled: true
        group: stable
---
spring:
  profiles: stable2
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: 101.35.83.149:8848
      config:
        file-extension: yaml
        server-addr: 101.35.83.149:8848
        refresh-enabled: true
        group: stable
---
spring:
  profiles: prod
  cloud:
    nacos:
      username: nacos
      password: dinglitec@nacosprod123
      discovery:
        server-addr: 172.18.1.14:8848,172.18.2.8:8848,172.18.3.12:8848
      config:
        file-extension: yaml
        server-addr: 172.18.1.14:8848,172.18.2.8:8848,172.18.3.12:8848
        refresh-enabled: true