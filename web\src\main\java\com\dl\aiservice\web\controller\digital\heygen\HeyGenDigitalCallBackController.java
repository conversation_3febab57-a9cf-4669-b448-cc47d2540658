package com.dl.aiservice.web.controller.digital.heygen;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.heygen.enums.HeyGenCallBackEnum;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.heygen.HeyGenDigitalService;
import com.dl.aiservice.biz.service.digital.ivh.IvhDigitalService;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.web.controller.digital.heygen.param.HeyGenCreateVideoCallBackParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/heygen")
@Slf4j
public class HeyGenDigitalCallBackController {

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Autowired
    private HeyGenDigitalService heyGenDigitalService;
    @Resource
    private IvhDigitalService ivhDigitalService;

    @Autowired
    private AiConfig aiConfig;

    @PostMapping("/callback")
    public void videoCreateCallBack(@RequestBody String requestBody, HttpServletRequest request){
        log.info("heygen合成数字人回调入参:" + requestBody);
        log.info("签名 = " + request.getHeader("signature"));
        String secret = aiConfig.getHeyGenSecret();
        String computedSignature = calculateHmacSHA256(secret, requestBody);
        log.info("computedSignature = " + computedSignature);
        if (!computedSignature.equals(request.getHeader("signature"))) {
            throw new RuntimeException("HeyGen回调验签失败");
        }
        log.info("HeyGen回调验签成功！");
        HeyGenCreateVideoCallBackParam param = HeyGenCreateVideoCallBackParam.getCallBackParam(requestBody);
        if (HeyGenCallBackEnum.SUCCESS.getEventType().equals(param.getEventType()) || HeyGenCallBackEnum.FAIL.getEventType().equals(param.getEventType())){
            //处理回调
            handleCallBack(param);
        }
    }

    /**
     * 处理生成视频回调
     * @param param
     */
    private void handleCallBack(HeyGenCreateVideoCallBackParam param){

        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
                .eq(MediaProduceJobPO::getChannel, ServiceChannelEnum.HEYGEN.getCode())
                .eq(MediaProduceJobPO::getExtJobId, param.getEventData().getVideoId()));

        if (Objects.isNull(mediaProduceJobPO)) {
            log.info("HeyGenCallbackParam = " + JSON.toJSONString(param));
            return;
        }


        //是否已超时
        Boolean hasTimeout = !MediaProduceJobTimeoutStatusEnum.UN.getStatus()
                .equals(mediaProduceJobPO.getTimeoutStatus());

        fillMediaProduceJobStatus(param,mediaProduceJobPO,hasTimeout);
        Long worksBizId = mediaProduceJobPO.getWorksBizId();
        DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
        BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);

        mediaProduceJobPO.setResponseDt(new Date());
        //上传cos
        String mediaUrl = heyGenDigitalService.getCosUrl(mediaProduceJobPO.getExtJobId(),param.getEventData().getUrl(),"mp4");

        mediaProduceJobPO.setMediaUrl(mediaUrl);
        mediaProduceJobManager.updateById(mediaProduceJobPO);

        //已超时则不回调业务
        if (hasTimeout) {
            return;
        }
        log.info("heygen执行回调DigitalVideoCallbackDTO=" + JSONUtil.toJsonStr(digitalCallbackDTO));
        //回调业务
        ivhDigitalService.callBackBiz(mediaProduceJobPO.getTenantCode(), worksBizId, mediaProduceJobPO.getCallbackUrl(),
                digitalCallbackDTO, JSONUtil.toJsonStr(param));

    }

    private void fillMediaProduceJobStatus(HeyGenCreateVideoCallBackParam param, MediaProduceJobPO mediaProduceJobPO,
            Boolean hasTimeout) {
        if (hasTimeout){
            if (HeyGenCallBackEnum.FAIL.getEventType().equals(param.getEventType())){
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
                return;
            }
            if (HeyGenCallBackEnum.SUCCESS.getEventType().equals(param.getEventType())){
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
                return;
            }
        }
        if (HeyGenCallBackEnum.FAIL.getEventType().equals(param.getEventType())){
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
        }
        if (HeyGenCallBackEnum.SUCCESS.getEventType().equals(param.getEventType())){
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
        }
    }


    private String calculateHmacSHA256(String key, String data) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hmacBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException ex) {
            throw new RuntimeException("Failed to calculate HMAC-SHA256", ex);
        }
    }
}
