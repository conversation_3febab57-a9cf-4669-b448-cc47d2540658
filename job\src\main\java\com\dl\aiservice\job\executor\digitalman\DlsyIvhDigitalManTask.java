package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.DlsyIvhDigitalManJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:56
 */
@Component
public class DlsyIvhDigitalManTask {

    @Resource
    private DlsyIvhDigitalManJobHandler dlsyIvhDigitalManJobHandler;

    @XxlJob("dlsyIvhDmVideoJob")
    public void dlsyIvhDmVideoJob() {
        dlsyIvhDigitalManJobHandler.handleDmVideoCreate();
    }
}
