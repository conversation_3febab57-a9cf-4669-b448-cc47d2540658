package com.dl.aiservice.biz.client.huawei.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HuaweiTokenRequest implements Serializable {


    private HuaweiAuthDTO auth;

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class HuaweiAuthDTO {
        private HuaweiIdentityDTO identity;
        private HuaweiScopeDTO scope;

        @Builder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Data
        public static class HuaweiIdentityDTO {
            private List<String> methods;
            private HwAkSkDTO hw_ak_sk;

            @Builder
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @Data
            public static class HwAkSkDTO {
                private HuaweiAccessDTO access;
                private HuaweiSecretDTO secret;

                @Builder
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @Data
                public static class HuaweiAccessDTO {
                    private String key;
                }

                @Builder
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @Data
                public static class HuaweiSecretDTO {
                    private String key;
                }
            }
        }

        @Builder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Data
        public static class HuaweiScopeDTO {
            private HuaweiProjectDTO project;

            @Builder
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @Data
            public static class HuaweiProjectDTO {
                private String name;
            }
        }
    }
}
