package com.dl.aiservice.share.videoproduce.dto.jimu;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JimuResult<T> {

    Boolean success;
    String code;
    String message;
    String msg;
    String requestId;
    T data;

    public static JimuResult error(String msg, String message, String code) {
        JimuResult result = new JimuResult();
        result.setCode(code);
        result.setMsg(msg);
        result.setMessage(message);
        result.setSuccess(false);
        return result;
    }

    public static JimuResult error(String msg, String code) {
        return error(msg, "", code);
    }
}
