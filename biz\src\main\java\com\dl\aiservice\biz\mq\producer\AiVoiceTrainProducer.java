package com.dl.aiservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dl.aiservice.biz.mq.dto.VoiceTrainProgressDTO;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 10:42
 */
@Slf4j
@Component
public class AiVoiceTrainProducer {

    @Autowired
    private AiChannels aiChannels;

    public void sendVoiceTrainProgressMQ(VoiceTrainProgressDTO progressRequestDTO, DelayLevelEnum delayLevelEnum) {
        Message message = MessageBuilder.withPayload(progressRequestDTO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayLevelEnum.getValue()).build();
        try {
            boolean sendResult = aiChannels.voicetrainprogressproducer().send(message, 1000L);
            log.info("发送声音训练查询进度请求的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
        } catch (Exception e) {
            log.error("发送声音训练查询进度请求的消息发生异常,message:{},e:{}", JSONUtil.toJsonStr(message), e);
        }
    }
}
