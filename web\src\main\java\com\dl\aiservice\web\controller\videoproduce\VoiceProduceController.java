package com.dl.aiservice.web.controller.videoproduce;

import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.videoproduce.dto.aliyun.ProduceMediaCompleteParamDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.JimuTaskCallbackParamDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/video/produce")
@Slf4j
public class VoiceProduceController {

    @Resource
    private VoiceProduceProcessor voiceProduceProcessor;
    
    /**
     * 视频合成
     *
     * @return
     */
    @ApiOperation("视频合成")
    @PostMapping("/handle")
    ResultModel<VideoProduceResponseDTO> produce(@RequestBody @Validated VideoProduceParamDTO param) {
        return voiceProduceProcessor.videoProduce(param);
    }

    @ApiOperation("新华智云合成结果回调接口")
    @PostMapping("/notify/jimu")
    @NotAuth
    public ResultModel<Boolean> handleJimuCallback(long timestamp, String signature,
            @RequestBody JimuTaskCallbackParamDTO param) {
        return voiceProduceProcessor.handleJimuCallback(timestamp, signature, param);
    }

    @Deprecated
    @ApiOperation("阿里云合成结果回调接口")
    @PostMapping("/notify/aliyun")
    @NotAuth
    public ResultModel<Boolean> handleAliyunCallback(@RequestBody ProduceMediaCompleteParamDTO param) {
        return voiceProduceProcessor.handleAliyunCallback(param);
    }

}