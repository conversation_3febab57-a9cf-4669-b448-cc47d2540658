package com.dl.aiservice.biz.manager.voiceclone.tencent;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.dl.aiservice.biz.client.ivh.IvhDigitalClient;
import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetSsmlTimeRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhSentences;
import com.dl.aiservice.biz.client.ivh.resp.IvhSsmlTimeResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhWords;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.service.digital.enums.IvhSynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.ivh.IvhDigitalService;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.aiservice.share.voiceclone.TtsSubtitleDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.qcloud.cos.http.HttpMethodName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName TencentVoiceCloneHandlerManager
 * @Description
 * <AUTHOR>
 * @Date 2023/5/10 21:30
 * @Version 1.0
 **/
@Component
@Slf4j
public class TencentVoiceCloneHandlerManager implements VoiceCloneHandlerManager {
    @Resource
    private IvhDigitalService ivhDigitalService;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private IvhDigitalClient ivhDigitalClient;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private SubtitleManager subtitleManager;

    private static Pattern sensitivePattern = Pattern.compile("\"Keywords\":\\[(.*?)\\]");

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.IVH, ServiceChannelEnum.FUJIA_IVH, ServiceChannelEnum.DLSY_IVH);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    /**
     * 特别注意：腾讯云的克隆音接口使用的是数字人(virtualManKey)来驱动的
     *
     * @param request
     * @return
     */
    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {
        long jobId = hostTimeIdg.generateId().longValue();
        IvhListenTtsRequestDTO ivhListenTtsRequest = new IvhListenTtsRequestDTO();
        if (NumberUtils.isNumber(request.getSpeed())) {
            ivhListenTtsRequest.setSpeed(Double.valueOf(request.getSpeed()));
        }
        ivhListenTtsRequest.setInputSsml(request.getText());
        ivhListenTtsRequest.setVirtualmanKey(request.getVoiceName());
        //清戈的声音暂时用陈超群的
        if (Objects.equals("d665a7029cd5487a802b7f9a8cf49456", request.getVoiceName())) {
            ivhListenTtsRequest.setTimbreKey("dl_chenchaoqun_dz");
        }
        if (Objects.equals("d34619ac7e0b45329055c69c247fae5d", request.getVoiceName())) {
            ivhListenTtsRequest.setTimbreKey("timbre_4565");
        }
        if(Objects.equals(request.getCustomStoreUrl(), Const.ONE)) {
            ivhListenTtsRequest.setAudioStorageS3Url(cosFileUploadManager.generatePresignedUrl(jobId+".mp3", HttpMethodName.PUT));
        }
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(jobId);
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(request.getWorksBizId());
        job.setVideoTaskJobId(request.getVideoTaskJobId());
        job.setChannel(channelUtil.getChannel());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(ivhListenTtsRequest));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);
        try {
            IvhListenTtsResponseDTO ivhListenTtsResponse = ivhDigitalService.listenTts(ivhListenTtsRequest);
            IvhSynthesisStatusEnum anEnum = IvhSynthesisStatusEnum.getEnum(ivhListenTtsResponse.getStatus());
            if (IvhSynthesisStatusEnum.SUCCESS.equals(anEnum)) {
                MediaProduceJobPO updateJob = new MediaProduceJobPO();
                updateJob.setId(job.getId());
                updateJob.setStatus(Const.ZERO);
                updateJob.setExtJobId(ivhListenTtsResponse.getIvhTaskId());
                updateJob.setMediaUrl(ivhListenTtsResponse.getMediaUrl());
                Double duration = getDuration(request, jobId, ivhListenTtsResponse);
                updateJob.setDuration(duration);
                updateJob.setResponseDt(new Date());
                mediaProduceJobManager.updateById(updateJob);
                TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
                ttsResponseDTO.setMediaJobId(job.getMediaJobId());
                ttsResponseDTO.setAudioUrl(ivhListenTtsResponse.getMediaUrl());
                ttsResponseDTO.setDuration(duration);

                if (Const.ONE.equals(request.getNeedSubtitle())) {
                    List<TtsSubtitleDTO> subtitleDTOList = subtitleManager
                            .genSubtitlesBySegmentor(request.getText(), request.getMaxLength(),
                                    ivhListenTtsResponse.getTextTimestampResult());
                    ttsResponseDTO.setSubtitles(subtitleDTOList);
                }

                return ResultModel.success(ttsResponseDTO);
            }
            MediaProduceJobPO updateJob = new MediaProduceJobPO();
            updateJob.setId(job.getId());
            updateJob.setStatus(-Const.ONE);
            updateJob.setExtJobId(ivhListenTtsResponse.getIvhTaskId());
            updateJob.setFailCode(ivhListenTtsResponse.getFailCode());
            updateJob.setFailReason(ivhListenTtsResponse.getFailMessage());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);

            return ResultModel.error("-1", "语音合成失败:" + this.buildErrorMsg(ivhListenTtsResponse));
        } catch (Exception e) {
            log.error("腾讯云tts语音合成发生异常,jobId:{},e:{}", jobId, e);
            MediaProduceJobPO updateJob = new MediaProduceJobPO();
            updateJob.setId(job.getId());
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            updateJob.setStatus(-Const.ONE);
            // 失败原因
            updateJob.setFailReason(e.getMessage());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }

    }

    private Double getDuration(TTSProduceParamDTO request, Long jobId, IvhListenTtsResponseDTO ivhListenTtsResponse)
            throws Exception {
        byte[] bytes = HttpUtil.createGet(ivhListenTtsResponse.getMediaUrl()).execute().bodyBytes();
        File audioFile = getAudioFile(String.valueOf(jobId), bytes, StringUtils.isBlank(request.getAudioEncode()) ? "wav" : request.getAudioEncode());
        Double duration = MediaUtil.getAudioDuration(audioFile);
        FileUtil.del(audioFile);
        return duration;
    }

    private File getAudioFile(String sessionId, byte[] data, String format) throws Exception {
        // 解码 Base64 数据
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

    /**
     * 获取数字人字幕
     *
     * @return
     */
    private List<TtsSubtitleDTO> subtitles(TTSProduceParamDTO requestDTO) {
        IvhGetSsmlTimeRequest request = new IvhGetSsmlTimeRequest();
        request.setInputSsml(requestDTO.getText());
        request.setVirtualmanKey(requestDTO.getVoiceName());
        if (NumberUtils.isNumber(requestDTO.getSpeed())) {
            request.setSpeed(Double.valueOf(requestDTO.getSpeed()));
        } else {
            request.setSpeed(1.0);
        }

        IvhBaseResponse<IvhSsmlTimeResponse> resp =
                ivhDigitalClient.getSsmlTime(IvhBaseRequest.<IvhGetSsmlTimeRequest>builder().payload(request).build());
        if (resp.isSuccess()) {
            List<TtsSubtitleDTO> subtitles = new ArrayList<>();
            for (IvhSentences sentence : resp.getPayload().getSentences()) {
                long lastEndTime = sentence.getWords().get(0).getStartTimestamp() / 10000;
                long beginTime = sentence.getWords().get(0).getStartTimestamp() / 10000;
                StringBuilder sb = new StringBuilder();
                for (IvhWords word : sentence.getWords()) {
                    long wordBeginTime = word.getStartTimestamp() / 10000;
                    long wordEndTime = word.getEndTimestamp() / 10000;
                    if (Objects.isNull(requestDTO.getMaxLength()) || sb.length() < requestDTO.getMaxLength()) {
                        sb.append(word.getWord());
                    } else {
                        //将已有sb，放入字幕对象中
                        TtsSubtitleDTO dmSubtitleDTO = new TtsSubtitleDTO();
                        dmSubtitleDTO.setText(trimSymbol(sb.toString()));
                        dmSubtitleDTO.setBeginTime(beginTime);
                        dmSubtitleDTO.setEndTime(lastEndTime);
                        subtitles.add(dmSubtitleDTO);

                        //新建个sb，拼接后续字符串
                        sb = new StringBuilder();
                        sb.append(word.getWord());
                        beginTime = wordBeginTime;
                    }
                    lastEndTime = wordEndTime;
                }
                if (sb.length() > 0) {
                    TtsSubtitleDTO dmSubtitleDTO = new TtsSubtitleDTO();
                    dmSubtitleDTO.setText(trimSymbol(sb.toString()));
                    dmSubtitleDTO.setBeginTime(beginTime);
                    dmSubtitleDTO.setEndTime(lastEndTime);
                    subtitles.add(dmSubtitleDTO);
                }
            }
            return subtitles;
        }
        return null;
    }

    private static String trimSymbol(String orignal) {
        if (StringUtils.isBlank(orignal)) {
            return orignal;
        }
        return orignal.replaceAll("【", "").replaceAll("】", "").replaceAll("。", "").replaceAll(",", "")
                .replaceAll("，", "").replaceAll("!", "").replaceAll("！", "").replaceAll("、", "").replaceAll("[?]", "")
                .replaceAll("？", "");
    }

    /**
     * 处理错误信息
     *
     * @param ivhListenTtsResponse
     * @return
     */
    private String buildErrorMsg(IvhListenTtsResponseDTO ivhListenTtsResponse) {
        //处理敏感词错误
        if (IvhErrCodeEnum.ERROR_CODE_801005.getErrorCode().toString().equals(ivhListenTtsResponse.getFailCode())) {
            String sensitiveWords = extractSensitiveWords(ivhListenTtsResponse.getFailMessage());
            if (StringUtils.isNotBlank(sensitiveWords)) {
                return "包含敏感词:" + sensitiveWords;
            }
            return "包含敏感词，请检查。";
        }
        return ivhListenTtsResponse.getFailMessage();
    }

    public static void main(String[] args) {
        String text1 = "InvalidParameterValue:参数取值错误: 文本内容安全审核未通过，包含敏感词: {\"Suggestion\":2,\"Keywords\":[\"恐怖分子\"],\"RequestId\":\"1ca05213-f8a1-401f-9285-d54e545eb9b4\"}, id: 1ca05213-f8a1-401f-9285-d54e545eb9b4";
        String text2 = "InvalidParameterValue:参数取值错误: 文本内容安全审核未通过，包含敏感词: {\"Suggestion\":2,\"Keywords\":[\"台独\"],\"RequestId\":\"363d6487-520c-4cee-9d3c-6634b5a95538\"}, id: 363d6487-520c-4cee-9d3c-6634b5a95538";

        String text3 = "InvalidParameterValue:参数取值错误: 文本内容安全审核未通过，包含敏感词: {\"Suggestion\":2,\"Keywords\":[\"疆独\",\"港独\"],\"RequestId\":\"6feaa52e-37f9-4be7-b2eb-f70ac8068ecb\"}, id: 6feaa52e-37f9-4be7-b2eb-f70ac8068ecb";
        String text4 = "InvalidParameterValue:参数取值错误: 文本内容安全审核未通过，拦截一级策略: Porn，二级策略: InsinuationPorn，id: 22c898ee-1609-4165-9c18-65b8de997f32";

        System.out.println(extractSensitiveWords(text1));
        System.out.println(extractSensitiveWords(text2));
        System.out.println(extractSensitiveWords(text3));
        System.out.println(extractSensitiveWords(text4));
    }

    /**
     * 从腾讯云错误信息中提取敏感词
     *
     * @param input
     */
    private static String extractSensitiveWords(String input) {
        try {
            Matcher matcher = sensitivePattern.matcher(input);
            Set<String> sensitiveWords = new HashSet<>();

            while (matcher.find()) {
                String keywordsJson = matcher.group(1);
                String[] keywords = keywordsJson.split("\",\"");

                for (String keyword : keywords) {
                    keyword = keyword.replaceAll("[\\[\\]\"]", "");
                    sensitiveWords.add(filterChineseCharacters(keyword));
                }
            }
            return String.join(SymbolE.PUNCTUATION_MARK_CN.getValue(), sensitiveWords);
        } catch (Exception e) {
            log.warn("从腾讯云错误信息中提取敏感词发生异常。input:{},e", input, e);
            return SymbolE.BLANK.getValue();
        }
    }

    /**
     * 过滤出汉字
     *
     * @param input
     * @return
     */
    private static String filterChineseCharacters(String input) {
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (isChinese(c)) {
                result.append(c);
            }
        }

        return result.toString();
    }

    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS;
    }

}
