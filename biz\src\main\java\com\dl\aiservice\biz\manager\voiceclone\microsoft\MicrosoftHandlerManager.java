package com.dl.aiservice.biz.manager.voiceclone.microsoft;

import cn.hutool.core.io.FileUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.*;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.microsoft.cognitiveservices.speech.*;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @describe: TencentCloudHandlerManager
 * @author: zhousx
 * @date: 2023/5/4 10:37
 */
@Component
@Slf4j
public class MicrosoftHandlerManager implements VoiceCloneHandlerManager {
    private static final String TENCENTCLOUD_ENDPOINT = "tts.tencentcloudapi.com";
    private TtsClient client;
    @Value("${dl.microsoft.speechKey}")
    private String microsoftSpeechKey;

    @Value("${dl.microsoft.speechRegion}")
    private String microsoftSpeechRegion;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;
    @Autowired
    private ChannelUtil channelUtil;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.MICROSOFT);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO param) {
        Assert.notNull(param, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "音频文本不能为空");

        // 实例化一个请求对象,每个接口都会对应一个request对象
        String sessionId = String.valueOf(hostTimeIdg.generateId().longValue());
        String codec = "wav";

        String ssmlText = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='zh-CN'>" +
                "<voice name='" + param.getVoiceName() + "'>" +
                replaceMicrosoftSsml(param.getText()) +
                "</voice></speak>";

        // 初始化请求数据
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(Long.valueOf(sessionId));
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(param.getWorksBizId());
        job.setVideoTaskJobId(param.getVideoTaskJobId());
        job.setChannel(ServiceChannelEnum.MICROSOFT.getCode());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(ssmlText));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);

        try {
            // 设置配置
            SpeechConfig config = SpeechConfig.fromSubscription(microsoftSpeechKey, microsoftSpeechRegion);
            config.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm);
            // 使用SSML文本初始化合成器
            SpeechSynthesizer synthesizer = new SpeechSynthesizer(config);
            SpeechSynthesisResult result = synthesizer.SpeakSsml(ssmlText);

            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                byte[] audioData = result.getAudioData();
                job.setResponseDt(new Date());
                File audioFile = getAudioFileByByte(sessionId, audioData, codec);
                String audioUrl = cosFileUploadManager.uploadFile(audioFile, null, null);
                Double duration = MediaUtil.getWavAudioDuration(audioFile);
                FileUtil.del(audioFile);

                job.setExtJobId(result.getResultId());
                job.setStatus(Const.ZERO);
                job.setMediaUrl(audioUrl);
                job.setDuration(duration);
                mediaProduceJobManager.updateById(job);

                TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
                ttsResponseDTO.setSid(result.getResultId());
                ttsResponseDTO.setMediaJobId(job.getMediaJobId());
                ttsResponseDTO.setAudioUrl(audioUrl);
                ttsResponseDTO.setDuration(duration);
                return ResultModel.success(ttsResponseDTO);

            } else {
                SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                log.error("CANCELED: Reason={}", cancellation.getReason());
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                // 失败原因
                job.setFailReason(cancellation.getErrorDetails());
                job.setResponseDt(new Date());
                mediaProduceJobManager.updateById(job);
                log.error(cancellation.getErrorDetails());
                return ResultModel.error("-1", "语音合成失败:" + cancellation.getErrorDetails());
            }
        } catch (Exception e) {
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            log.error(e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private File getAudioFileByByte(String sessionId, byte[] data, String format) throws Exception {
        // 解码 Base64 数据
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

    /**
     * 备注：多音字兼容处理
     * alphabet="py" 修改为 alphabet="sapi"
     * 拼音中间加空格 ph = "zhuan3" 修改为 ph = "zhuan 3"
     * interpret-as="digit" 替换为 interpret-as="number_digit“
     * @param ssml
     * @return
     */
    public static String replaceMicrosoftSsml(String ssml) {
        if (StringUtils.isBlank(ssml)) {
            return StringUtils.EMPTY;
        }
        ssml = ssml.replaceAll("alphabet=\"py\"", "alphabet=\"sapi\"").replaceAll("interpret-as=\"digits\"", "interpret-as=\"number_digit\"");
        // 正则表达式，匹配ph属性，并在数字前加空格
        String regex = "(<phoneme\\s+[^>]*?ph=\")([^\"]*?)(\\d+)(\">)";
        // 替换字符串，$1是<phoneme...ph="，$2是除了最后一个数字之外的部分，$3是最后一个数字，$4是">
        // 在$3（最后一个数字）前加一个空格
        String replacement = "$1$2 $3$4";

        // 使用Pattern和Matcher来进行替换
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(ssml);
        // 使用StringBuffer来构建修改后的字符串（因为String是不可变的）
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        return result.toString();
    }
}
