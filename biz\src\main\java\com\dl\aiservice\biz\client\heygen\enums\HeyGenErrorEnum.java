package com.dl.aiservice.biz.client.heygen.enums;

import com.dl.aiservice.biz.client.guiji.enums.GjErrCodeEnum;

import java.util.Objects;

public enum HeyGenErrorEnum {
    Error_CODE_100(100,"正常"),
    Error_CODE_40118(40118,"无法将请求正文解码为 JSON"),
    Error_CODE_40012(40012,"查询参数或请求正文无效"),
    Error_CODE_400128(400128,"查询参数或请求正文无效"),
    Error_CODE_400123(400123,"超出速率限制"),
    Error_CODE_40102(40102,"未经过授权"),
    Error_CODE_40056(40056,"请求参数不匹配"),
    UNKNOWN(9999999, "未知异常");
    ;

    private final Integer errorCode;
    private final String errorDesc;

    HeyGenErrorEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (HeyGenErrorEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static HeyGenErrorEnum errorCode(Integer code) {
        for (HeyGenErrorEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
