package com.dl.aiservice.biz.service.videomake.jimu;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.callback.JimuVideoProduceCallBackClient;
import com.dl.aiservice.biz.client.jimu.JimuService;
import com.dl.aiservice.biz.client.jimu.dto.JimuExtInfo;
import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.client.jimu.dto.MediaAsrInfo;
import com.dl.aiservice.biz.client.jimu.dto.MediaAsrListInfo;
import com.dl.aiservice.biz.client.jimu.dto.MediaInfo;
import com.dl.aiservice.biz.client.jimu.dto.TemplateVarInfo;
import com.dl.aiservice.biz.client.jimu.dto.ValueFormat;
import com.dl.aiservice.biz.client.jimu.dto.VarInfo;
import com.dl.aiservice.biz.client.jimu.enums.ReplaceTypeEnum;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.dal.po.VideoTaskJobPO;
import com.dl.aiservice.biz.manager.AiVirtualManManager;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.manager.VideoTaskJobManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManAndVoiceBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceBO;
import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.enums.VoiceCloneEnum;
import com.dl.aiservice.biz.register.VideoProduceHelper;
import com.dl.aiservice.biz.register.VoiceCloneHelper;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.videomake.AbstractVideoMakeService;
import com.dl.aiservice.biz.service.videomake.VideoMakeService;
import com.dl.aiservice.biz.service.videomake.enums.VideoMakeJobErrStatusEnum;
import com.dl.aiservice.biz.service.videomake.jimu.bo.InnerErrNotifyParamBO;
import com.dl.aiservice.biz.service.videomake.jimu.bo.TemplateVarBO;
import com.dl.aiservice.biz.service.videomake.jimu.bo.UnitBO;
import com.dl.aiservice.biz.service.videomake.jimu.bo.VarBO;
import com.dl.aiservice.biz.service.videomake.jimu.bo.XhVarMappingBO;
import com.dl.aiservice.share.digitalman.DigitalManVideoGenResultDTO;
import com.dl.aiservice.share.enums.DigitalManChannelEnum;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.ReviseAsrRequestDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrResponseDTO;
import com.dl.aiservice.share.videomake.BaseMessageDTO;
import com.dl.aiservice.share.videomake.DefaultMessageDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.videoproduce.dto.VideoProduceCallbackBizParamDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.GlobalVarDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.JimuDigitalManInfoDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.TemplateVarDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.UnitVarDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.VarDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName JimuVideoMakeServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 20:00
 * @Version 1.0
 **/
@Component
@Slf4j
public class JimuVideoMakeService extends AbstractVideoMakeService {

    private static final String BGM = "bgm";
    private static final String AUDIO = "audio";
    private static final int THREAD_SLEEP_TIMES = 2000;
    private static final String THIRD_DG_CALLBACK_URI = "%s/aiservice/video/make/callback/dg";

    @Resource
    private SubtitleManager subtitleManager;
    @Resource
    private VideoProduceHelper videoProduceHelper;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private JimuService jimuService;
    @Resource
    private VideoTaskJobManager videoTaskJobManager;
    @Resource
    private ExecutorService ttsCloneVoiceTaskExecutor;
    @Resource
    private ExecutorService syncVirtualManVideoGenExecutor;
    @Resource
    private VoiceCloneHelper voiceCloneHelper;
    @Resource
    private AiVirtualManManager aiVirtualManManager;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private JimuVideoProduceCallBackClient jimuVideoProduceCallBackClient;
    @Resource
    private CallbackLogManager callbackLogManager;

    @Override
    public MediaProduceChannelEnum getEnum() {
        return MediaProduceChannelEnum.XHZY;
    }

    @Override
    public void checkMessage(BaseMessageDTO message) {
        predicate(message instanceof DefaultMessageDTO, VideoMakeJobErrStatusEnum.CODE_49.getCode().toString(),
                "message入参非法");
        DefaultMessageDTO defaultMessage = (DefaultMessageDTO) message;
        predicate(CollectionUtils.isNotEmpty(defaultMessage.getUnitCardList()),
                VideoMakeJobErrStatusEnum.CODE_49.getCode().toString(), "合成视频报文变量信息为空");
        predicate(StringUtils.isNotBlank(message.getThirdTemplateId()),
                VideoMakeJobErrStatusEnum.CODE_49.getCode().toString(), "第三方厂商模板ID必填");
        predicate(StringUtils.isNotBlank(message.getCallbackUrl()),
                VideoMakeJobErrStatusEnum.CODE_49.getCode().toString(), "回调URL必填");
    }

    @Override
    public void makeVideoHomology(String tenantCode, BaseMessageDTO message,
            DaVirtualManAndVoiceBO daVirtualManAndVoice, Long workBizId, Long videoTaskJobId) {
        TemplateVarBO templateVarBO = getTemplateVarBO((DefaultMessageDTO) message, daVirtualManAndVoice);
        ResultModel<VideoProduceResponseDTO> result = videoProduceHelper.get(ServiceChannelEnum.XHZY)
                .produce(getVideoProduceParam(tenantCode, templateVarBO, videoTaskJobId, workBizId));
        if (!result.isSuccess()) {
            innerErrNotify(InnerErrNotifyParamBO.builder().videoTaskJobId(videoTaskJobId).worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_51.getCode()).failReason(result.getMessage())
                    .callbackUrl(templateVarBO.getCallbackUrl()).build());
        }
    }

    @Override
    public void makeVideoByThirdTTS(String tenantCode, BaseMessageDTO message, DaVirtualVoiceBO daVirtualVoice,
            Long workBizId, Long videoTaskJobId) {
        TemplateVarBO templateVarBO = getTemplateVarBO((DefaultMessageDTO) message);
        try {
            ttsProduce(templateVarBO.getTemplateVariables(), daVirtualVoice, videoTaskJobId, workBizId, tenantCode);
        } catch (Exception e) {
            log.error("makeVideoByThirdTTS>>ttsProduce>>exception!", e);
            innerErrNotify(InnerErrNotifyParamBO.builder().videoTaskJobId(videoTaskJobId).worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_52.getCode()).failReason(e.getMessage())
                    .callbackUrl(templateVarBO.getCallbackUrl()).build());
            return;
        }
        ResultModel<VideoProduceResponseDTO> result = videoProduceHelper.get(ServiceChannelEnum.XHZY)
                .produce(getVideoProduceParam(tenantCode, templateVarBO, videoTaskJobId, workBizId));
        if (!result.isSuccess()) {
            innerErrNotify(InnerErrNotifyParamBO.builder().videoTaskJobId(videoTaskJobId).worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_53.getCode()).failReason(result.getMessage())
                    .callbackUrl(templateVarBO.getCallbackUrl()).build());
        }
    }

    @Override
    public void makeVideoByThirdVirtualMan(BaseMessageDTO message, DaVirtualManAndVoiceBO daVirtualManAndVoice,
            Long workBizId, Long videoTaskJobId, String tenantCode) {
        TemplateVarBO templateVarBO = getTemplateVarBO((DefaultMessageDTO) message);
        DaVirtualManScenesBO daVirtualManScenes = daVirtualManAndVoice.getDaVirtualManScenes();
        DaVirtualVoiceBO daVirtualVoice = daVirtualManAndVoice.getDaVirtualVoice();
        try {
            ttsProduce(templateVarBO.getTemplateVariables(), daVirtualVoice, videoTaskJobId, workBizId, tenantCode);
        } catch (Exception e) {
            log.error("makeVideoByThirdVirtualMan>>ttsProduce>>exception!", e);
            innerErrNotify(InnerErrNotifyParamBO.builder().videoTaskJobId(videoTaskJobId).worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_52.getCode()).failReason(e.getMessage())
                    .callbackUrl(templateVarBO.getCallbackUrl()).build());
            return;
        }
        try {
            videoProduce(workBizId, templateVarBO, daVirtualManScenes, daVirtualVoice, videoTaskJobId, tenantCode);
        } catch (Exception e) {
            log.error("makeVideoByThirdVirtualMan>>videoProduce>>exception!", e);
            innerErrNotify(InnerErrNotifyParamBO.builder().videoTaskJobId(videoTaskJobId).worksBizId(workBizId)
                    .jobStatus(VideoMakeJobErrStatusEnum.CODE_54.getCode()).failReason(e.getMessage())
                    .callbackUrl(templateVarBO.getCallbackUrl()).build());
            return;
        }
        savePayload(videoTaskJobId, templateVarBO);
    }

    private void savePayload(Long videoTaskJobId, TemplateVarBO templateVarBO) {
        try {
            // 中间临时报文要放入redis缓存
            redisUtil.set(VideoMakeService.KEY_TEMP_MSG_PREFIX + videoTaskJobId, JsonUtils.toJSON(templateVarBO),
                    VideoMakeService.REDIS_TEMP_MSG_TIMEOUT);
        } catch (Exception e) {
            log.error("中间报文存入redis异常！", e);
        }
        videoTaskJobManager.lambdaUpdate().eq(VideoTaskJobPO::getJobId, videoTaskJobId)
                .set(VideoTaskJobPO::getTempMsg, JsonUtils.toJSON(templateVarBO))
                .set(VideoTaskJobPO::getModifyDt, new Date()).update();
    }

    @Override
    public void afterThirdVirtualManDone(String tenantCode, Long videoTaskJobId,
            List<DigitalManVideoGenResultDTO> videoList) {
        VideoTaskJobPO taskJob = videoTaskJobManager.lambdaQuery().eq(VideoTaskJobPO::getJobId, videoTaskJobId).one();
        Assert.notNull(taskJob, String.format("videoTaskJobId[%s]不存在", videoTaskJobId));
        TemplateVarBO templateVar = JsonUtils.fromJSON(getPayload(videoTaskJobId), TemplateVarBO.class);
        Assert.notNull(templateVar, String.format("videoTaskJobId[%s]缓存中无临时报文信息", videoTaskJobId));
        // 视频素材 上传积木
        uploadMedia(taskJob, templateVar, videoList);
        ResultModel<VideoProduceResponseDTO> result = videoProduceHelper.get(ServiceChannelEnum.XHZY)
                .produce(getVideoProduceParam(tenantCode, templateVar, videoTaskJobId, taskJob.getWorksBizId()));
        Assert.isTrue(result.isSuccess(), "提交积木视频合成失败,message[" + result.getMessage() + "]");
    }

    private String getPayload(Long videoTaskJobId) {
        String tempMsg = redisUtil.get(VideoMakeService.KEY_TEMP_MSG_PREFIX + videoTaskJobId);
        if (StringUtils.isEmpty(tempMsg)) {
            VideoTaskJobPO taskJob =
                    videoTaskJobManager.lambdaQuery().eq(VideoTaskJobPO::getJobId, videoTaskJobId).one();
            return taskJob.getTempMsg();
        }
        return tempMsg;
    }

    @Override
    public void innerErrNotify(InnerErrNotifyParamBO param) {
        log.info("内部原因合成失败主动回调，param={}", JsonUtils.toJSON(param));
        VideoProduceCallbackBizParamDTO callbackBizParamDTO = new VideoProduceCallbackBizParamDTO();
        callbackBizParamDTO.setWorksBizId(param.getWorksBizId());
        callbackBizParamDTO.setJobStatus(param.getJobStatus());
        callbackBizParamDTO.setExtJobId(param.getVideoTaskJobId().toString());
        callbackBizParamDTO.setFailReason(param.getFailReason());
        //回调业务
        ResultModel callbackResp = jimuVideoProduceCallBackClient.callback(param.getCallbackUrl(), callbackBizParamDTO);
        //记录日志
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(callbackBizParamDTO.getExtJobId());
        callbackLog.setCallbackType(Const.ONE);
        callbackLog.setChannel(MediaProduceChannelEnum.XHZY.getCode());
        callbackLog.setExtCallbackRespBody(JsonUtils.toJSON(param));
        callbackLog.setStatus(callbackResp.isSuccess() ? Const.ONE : Const.TWO);
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResp));
        callbackLogManager.save(callbackLog);
    }

    private void videoProduce(Long workBizId, TemplateVarBO templateVarBO, DaVirtualManScenesBO daVirtualManScenes,
            DaVirtualVoiceBO daVirtualVoice, Long videoTaskJobId, String tenantCode) {
        // 按照插入顺序排序
        Map<String, CompletableFuture<String>> futureMap = Maps.newLinkedHashMap();
        // 分两次处理，优先处理VideoTTS变量，因为tts一般无回调，所以保证tts先合成，然后在处理VirtualHost变量
        templateVarBO.getTemplateVariables().stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables()))
                .forEach(unit -> {
                    unit.getVariables().stream().filter(x -> isUndo(x.getReplaceType()))
                            .filter(x -> StringUtils.endsWithIgnoreCase(x.getKey(), Const.MAIN_VIDEO_SUFFIX))
                            .forEach(x -> {
                                String ttsCnt = cleanStr(String.valueOf(x.getValue()));
                                if (StringUtils.isBlank(ttsCnt)) {
                                    return;
                                }
                                x.setTts(ttsCnt);
                                futureMap.put(x.getKey(),
                                        videoCreateFuture(ttsCnt, videoTaskJobId, daVirtualManScenes, daVirtualVoice,
                                                workBizId, tenantCode));
                            });
                });
        HashMap<String, String> resultMap = Maps.newHashMap();
        futureMap.entrySet().stream().forEach(entry -> {
            try {
                resultMap.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                log.error("videoCreateFuture>>exception!myWorkBizId={}", workBizId, e);
                throw BusinessServiceException
                        .getInstance(Objects.nonNull(e.getCause()) ? e.getCause().getMessage() : e.getMessage());
            }
        });
        templateVarBO.getTemplateVariables().stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables()))
                .forEach(unit -> {
                    unit.getVariables().stream().filter(x -> resultMap.containsKey(x.getKey())).forEach(x -> {
                        x.setValue(resultMap.get(x.getKey()));
                    });
                });
    }

    private CompletableFuture<String> videoCreateFuture(String inputTts, Long videoTaskJobId,
            DaVirtualManScenesBO daVirtualManScenes, DaVirtualVoiceBO daVirtualVoice, Long workBizId,
            String tenantCode) {
        if (Objects.equals(daVirtualManScenes.getChannel(), daVirtualVoice.getChannel()) && Objects
                .equals(daVirtualVoice.getVoiceType(), Const.ONE)) {
            // 同源克隆音处理
            return CompletableFuture.supplyAsync(() -> {
                CreateRequestDTO request = getCreateRequest(videoTaskJobId, daVirtualManScenes, daVirtualVoice,
                        workBizId);
                request.setType(Const.ONE);
                request.setText(inputTts);
                CreateResponseDTO createResponse = aiVirtualManManager.videoCreate(tenantCode, request);
                return createResponse.getMediaJobId().toString();
            }, syncVirtualManVideoGenExecutor);
        }
        CompletableFuture<String> tts = this.tts(inputTts, daVirtualVoice, videoTaskJobId, workBizId, tenantCode);
        return tts.thenApply((result) -> {
            CreateRequestDTO request = getCreateRequest(videoTaskJobId, daVirtualManScenes, daVirtualVoice, workBizId);
            request.setAudioUrl(result);
            request.setType(Const.ZERO);
            CreateResponseDTO createResponse = aiVirtualManManager.videoCreate(tenantCode, request);
            return createResponse.getMediaJobId().toString();
        });
    }

    private CreateRequestDTO getCreateRequest(Long videoTaskJobId, DaVirtualManScenesBO daVirtualManScenes,
            DaVirtualVoiceBO daVirtualVoice, Long workBizId) {
        CreateRequestDTO request = new CreateRequestDTO();
        request.setCallbackUrl(String.format(THIRD_DG_CALLBACK_URI, getCallbackHost()));
        request.setSceneId(daVirtualManScenes.getSceneId());
        if (!Objects.equals(DigitalManChannelEnum.IVH.getCode(), daVirtualManScenes.getChannel())) {
            request.setSpeakerId(daVirtualVoice.getVoiceKey());
        }
        request.setChannel(daVirtualManScenes.getChannel());
        request.setWorksBizId(workBizId);
        request.setVideoTaskJobId(videoTaskJobId);
        return request;
    }

    public CompletableFuture<String> tts(final String inputText, final DaVirtualVoiceBO daVirtualVoice,
            final Long videoTaskJobId, final Long workBizId, String tenantCode) {
        return CompletableFuture.supplyAsync(() -> {
            TTSResponseDTO ttsResult = getTTSResult(inputText, daVirtualVoice, videoTaskJobId, workBizId, tenantCode);
            return ttsResult.getAudioUrl();
        }, syncVirtualManVideoGenExecutor);
    }

    private VideoProduceParamDTO getVideoProduceParam(String tenantCode, TemplateVarBO templateVarBO,
            Long videoTaskJobId, Long workBizId) {
        VideoProduceParamDTO videoProduceParam = new VideoProduceParamDTO();
        videoProduceParam.setTenantCode(tenantCode);
        videoProduceParam.setWorksBizId(workBizId);
        videoProduceParam.setVideoTaskJobId(videoTaskJobId);
        videoProduceParam.setTemplateVar(getTemplateVar(templateVarBO));
        return videoProduceParam;
    }

    private TemplateVarDTO getTemplateVar(TemplateVarBO templateVarBO) {
        TemplateVarInfo varInfoFromRemote = getTemplateVarInfoFromJimu(templateVarBO);
        TemplateVarDTO templateVar = new TemplateVarDTO();
        templateVar.setTemplateId(templateVarBO.getTemplateId());
        templateVar.setCallbackUrl(templateVarBO.getCallbackUrl());
        templateVar.setTemplateVariables(getTemplateUnitVars(templateVarBO, varInfoFromRemote));
        // 设置背景音乐
        templateVar.setGlobalVariables(getGlobalVariables(templateVarBO.getBgmUrl(), varInfoFromRemote));
        return templateVar;
    }

    /**
     * 将文本转为音频，并提交智云进行字幕提取
     *
     * @param templateVariables
     * @param daVirtualVoice
     * @param videoTaskJobId
     * @param workBizId
     * @return
     */
    private void ttsProduce(List<UnitBO> templateVariables, DaVirtualVoiceBO daVirtualVoice, Long videoTaskJobId,
            Long workBizId, String tenantCode) {
        if (CollectionUtils.isEmpty(templateVariables)) {
            return;
        }
        Map<String, Future<String>> futureMap = Maps.newLinkedHashMap();
        templateVariables.stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables())).forEach(unit -> {
            // 这里去处理virtualVars中的 克隆音变量
            unit.getVariables().stream().filter(x -> isUndo(x.getReplaceType()))
                    .filter(x -> StringUtils.endsWithIgnoreCase(x.getKey(), Const.MAIN_AUDIO_SUFFIX)).forEach(x -> {
                String value = cleanStr(String.valueOf(x.getValue()));
                if (StringUtils.isBlank(value)) {
                    return;
                }
                futureMap.put(x.getKey(), ttsCloneVoiceTaskExecutor.submit(() -> {
                    TTSResponseDTO ttsResult = getTTSResult(value, daVirtualVoice, videoTaskJobId, workBizId,
                            tenantCode);
                    JimuResult<MediaInfo> result = jimuService.addMedia(ttsResult.getAudioUrl(), Const.JIMU_ASR_TAG);
                    if (!result.getSuccess() || Objects.isNull(result.getData()) || StringUtils
                            .isBlank(result.getData().getMediaId())) {
                        log.error("tts文件上传失败！myWorkBizId={},param={},resp={}", workBizId, ttsResult.getAudioUrl(),
                                JSONUtil.toJsonStr(result));
                        throw BusinessServiceException
                                .getInstance("视频合成任务生成异常，上传素材失败audioUrl[" + ttsResult.getAudioUrl() + "]");
                    }
                    return result.getData().getMediaId();
                }));
            });
        });
        HashMap<String, String> resultMap = Maps.newHashMap();
        futureMap.entrySet().stream().forEach(entry -> {
            try {
                resultMap.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                log.error("ttsProduce>>exception!", e);
                throw BusinessServiceException
                        .getInstance(Objects.nonNull(e.getCause()) ? e.getCause().getMessage() : e.getMessage());
            }
        });
        // 字幕校验
        templateVariables.stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables())).forEach(
                unit -> unit.getVariables().stream().filter(x -> resultMap.containsKey(x.getKey())).forEach(x -> {
                    String value = String.valueOf(x.getValue());
                    String mediaId = resultMap.get(x.getKey());
                    x.setValue(mediaId);
                    checkSubtitle(mediaId, value, Const.ZERO, videoTaskJobId);
                }));
    }

    private TTSResponseDTO getTTSResult(String inputText, DaVirtualVoiceBO daVirtualVoice, Long videoTaskJobId,
            Long workBizId, String tenantCode) {
        VoiceCloneEnum e = VoiceCloneEnum.getByCode(daVirtualVoice.getChannel());
        Assert.notNull(e, "该渠道暂不支持声纹克隆");
        TTSProduceParamDTO param = new TTSProduceParamDTO();
        param.setVoiceName(daVirtualVoice.getVoiceKey());
        if (Objects.equals(daVirtualVoice.getChannel(), VoiceCloneEnum.DEEP_SOUND.getCode())) {
            inputText = Base64.encodeUrlSafe(inputText, StandardCharsets.UTF_8);
        }
        param.setText(inputText);
        param.setWorksBizId(workBizId);
        param.setVideoTaskJobId(videoTaskJobId);
        param.setVolume(daVirtualVoice.getVolume());
        param.setEmotionCategory(daVirtualVoice.getEmotionCategory());
        param.setEmotionIntensity(daVirtualVoice.getEmotionIntensity());
        param.setPitch(daVirtualVoice.getPitch());
        param.setSpeed(daVirtualVoice.getSpeed());
        VoiceCloneHandlerManager ttsHandler = voiceCloneHelper.get(ServiceChannelEnum.getByCode(e.getCode()));
        Assert.notNull(ttsHandler, "该渠道暂不支持声纹克隆");
        channelUtil.init(tenantCode, daVirtualVoice.getChannel());
        ResultModel<TTSResponseDTO> ttsResp = ttsHandler.ttsProduce(param);
        if (!ttsResp.isSuccess()) {
            log.error("ttsProduce err! voiceDetail={},param={},resp={}", JSONUtil.toJsonStr(daVirtualVoice),
                    JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(ttsResp));
            throw BusinessServiceException.getInstance(ttsResp.getMessage());
        }
        return ttsResp.getDataResult();
    }

    private TemplateVarInfo getTemplateVarInfoFromJimu(TemplateVarBO templateVarBO) {
        JimuResult<TemplateVarInfo> jimuVars = jimuService.getVariables(templateVarBO.getTemplateId());
        TemplateVarInfo varInfoFromRemote = jimuVars.getData();
        if (!jimuVars.getSuccess()) {
            Assert.notNull(varInfoFromRemote, String.format("积木模板%s不存在", templateVarBO.getTemplateId()));
        }
        checkDuplicateVarDesc(varInfoFromRemote);
        return varInfoFromRemote;
    }

    private List<UnitVarDTO> getTemplateUnitVars(TemplateVarBO templateVarBO, TemplateVarInfo varInfoFromRemote) {
        List<VarBO> sourceVarList = Lists.newArrayList();
        templateVarBO.getTemplateVariables().stream().forEach(unit -> sourceVarList.addAll(unit.getVariables()));
        Map<String, XhVarMappingBO> xhVarMappingMap = getXhVarMappingMap(varInfoFromRemote);
        // 拼接积木最终使用的var信息
        Map<String, List<VarDTO>> templateVariables = new HashMap<>();
        sourceVarList.stream().forEach(t -> {
            XhVarMappingBO bo = xhVarMappingMap.get(t.getKey());
            if (Objects.isNull(bo)) {
                return;
            }
            String xhUnitKey = bo.getUnitKey();
            List<VarDTO> list = templateVariables.get(xhUnitKey);
            if (list == null) {
                list = Lists.newArrayList();
                templateVariables.put(xhUnitKey, list);
            }
            //值格式化
            Object value = t.getValue();
            if (bo.getFormatType() == Const.ZERO) {
                list.add(new VarDTO(bo.getKey(), toStringValue(value), t.getReplaceType(), convert(t.getExtInfo())));
            } else if (bo.getFormatType() == Const.ONE) {
                list.add(new VarDTO(bo.getKey(), new ValueFormat(toStringValue(value)), t.getReplaceType(),
                        convert(t.getExtInfo())));
            } else {
                Number number = toNumber(toStringValue(value));
                if (Objects.nonNull(number)) {
                    value = number;
                }
                value = Objects.isNull(value) ? StringUtils.EMPTY : value;
                list.add(new VarDTO(bo.getKey(), value, t.getReplaceType(), convert(t.getExtInfo())));
            }
        });
        return templateVariables.entrySet().stream().map(t -> {
            String unitKey = t.getKey();
            List<VarDTO> vars = t.getValue();
            UnitVarDTO unitVar = new UnitVarDTO();
            unitVar.setUnitKey(unitKey);
            unitVar.setVariables(vars);
            return unitVar;
        }).collect(Collectors.toList());
    }

    private JimuDigitalManInfoDTO convert(JimuExtInfo source) {
        if (source == null) {
            return null;
        }
        JimuDigitalManInfoDTO jimuDigitalManInfoDTO = new JimuDigitalManInfoDTO();
        jimuDigitalManInfoDTO.setAvatar(source.getAvatar());
        jimuDigitalManInfoDTO.setPer(source.getPer());
        jimuDigitalManInfoDTO.setShowSubtitle(source.getShowSubtitle());
        return jimuDigitalManInfoDTO;
    }

    /**
     * 处理背景音乐配置
     *
     * @param bgmUrl
     * @param varInfoFromRemote
     * @return
     */
    private List<GlobalVarDTO> getGlobalVariables(String bgmUrl, TemplateVarInfo varInfoFromRemote) {
        List<VarInfo> globalVariables = varInfoFromRemote.getGlobalVariables();
        if (StringUtils.isBlank(bgmUrl) || CollectionUtils.isEmpty(globalVariables)) {
            return Lists.newArrayList();
        }
        Optional<VarInfo> bgmOpt = globalVariables.stream()
                .filter(x -> StringUtils.equals(BGM, x.getName()) && StringUtils.equals(AUDIO, x.getType()))
                .findFirst();
        if (!bgmOpt.isPresent()) {
            return Lists.newArrayList();
        }
        VarInfo varInfo = bgmOpt.get();
        GlobalVarDTO globalVar = new GlobalVarDTO();
        globalVar.setType(varInfo.getType());
        globalVar.setKey(varInfo.getKey());
        globalVar.setName(varInfo.getName());
        globalVar.setValue(bgmUrl);
        return Lists.newArrayList(globalVar);
    }

    private Map<String, XhVarMappingBO> getXhVarMappingMap(TemplateVarInfo result) {
        Map<String, XhVarMappingBO> map = new LinkedHashMap();
        if (Objects.isNull(result)) {
            return map;
        }
        if (CollectionUtils.isEmpty(result.getTemplateVariables())) {
            return map;
        }
        result.getTemplateVariables().forEach(unitInfo -> {
            List<VarInfo> vars = unitInfo.getVariables();
            if (CollectionUtils.isNotEmpty(vars)) {
                vars.forEach(varInfo -> {
                    XhVarMappingBO bo = new XhVarMappingBO();
                    bo.setUnitKey(unitInfo.getUnitKey());
                    bo.setKey(varInfo.getKey());
                    bo.setName(varInfo.getName());
                    if (varInfo.getDataFormat() instanceof ValueFormat) {
                        bo.setFormatType(Const.ONE);
                    } else if (varInfo.getDataFormat() == null || varInfo.getDataFormat() instanceof CharSequence
                            || varInfo.getDataFormat() instanceof TextNode) {
                        bo.setFormatType(Const.ZERO);
                    } else {
                        bo.setFormatType(Const.TWO);
                    }
                    map.put(varInfo.getName(), bo);
                });
            }
        });
        return map;
    }

    private TemplateVarBO getTemplateVarBO(DefaultMessageDTO defaultMessage,
            DaVirtualManAndVoiceBO daVirtualManAndVoice) {
        TemplateVarBO templateVarBO = getTemplateVarBO(defaultMessage);
        templateVarBO.getTemplateVariables().stream()
                .forEach(u -> u.getVariables().stream().filter(v -> isUndo(v.getReplaceType())).forEach(v -> {
                    if (StringUtils.endsWith(v.getKey(), Const.MAIN_AUDIO_SUFFIX)) {
                        JimuExtInfo digitalManInfo = new JimuExtInfo();
                        digitalManInfo.setPer(daVirtualManAndVoice.getDaVirtualVoice().getVoiceKey());
                        v.setExtInfo(digitalManInfo);
                    } else if (StringUtils.endsWith(v.getKey(), Const.MAIN_VIDEO_SUFFIX) && Objects
                            .nonNull(daVirtualManAndVoice.getDaVirtualManScenes())) {
                        JimuExtInfo digitalManInfo = new JimuExtInfo();
                        digitalManInfo.setAvatar(daVirtualManAndVoice.getDaVirtualManScenes().getSceneId());
                        digitalManInfo.setPer(daVirtualManAndVoice.getDaVirtualVoice().getVoiceKey());
                        v.setExtInfo(digitalManInfo);
                    }
                }));
        return templateVarBO;
    }

    private boolean isUndo(String replaceType) {
        return StringUtils.equals(ReplaceTypeEnum.UNDO.getCode(), replaceType);
    }

    private TemplateVarBO getTemplateVarBO(DefaultMessageDTO defaultMessage) {
        TemplateVarBO templateVarBO = new TemplateVarBO();
        templateVarBO.setBgmUrl(defaultMessage.getBgmUrl());
        templateVarBO.setTemplateId(defaultMessage.getThirdTemplateId());
        templateVarBO.setCallbackUrl(defaultMessage.getCallbackUrl());
        List<UnitBO> templateVariables = defaultMessage.getUnitCardList().stream().map(u -> {
            UnitBO unitBO = new UnitBO();
            unitBO.setUnitKey(u.getUnitKey());
            unitBO.setVariables(
                    u.getVariables().stream().map(v -> new VarBO(v.getKey(), v.getValue(), v.getReplaceType()))
                            .collect(Collectors.toList()));
            return unitBO;
        }).collect(Collectors.toList());
        templateVarBO.setTemplateVariables(templateVariables);
        return templateVarBO;
    }

    /**
     * 校验重复的变量描述
     *
     * @param varInfoFromRemote
     */
    private void checkDuplicateVarDesc(TemplateVarInfo varInfoFromRemote) {
        List<VarInfo> varInfoList = Lists.newArrayList();
        varInfoFromRemote.getTemplateVariables().stream().forEach(x -> varInfoList.addAll(x.getVariables()));
        Map<String, List<VarInfo>> varNameMap = varInfoList.stream().collect(Collectors.groupingBy(VarInfo::getName));
        boolean isPresent =
                varNameMap.entrySet().stream().filter(e -> e.getValue().size() > Const.ONE).findAny().isPresent();
        if (isPresent) {
            List<String> nameList =
                    varNameMap.entrySet().stream().filter(e -> e.getValue().size() > Const.ONE).map(e -> e.getKey())
                            .collect(Collectors.toList());
            log.error("积木模板中变量重命名重复,templateId={},dupNames={}", varInfoFromRemote.getTemplateId(),
                    JsonUtils.toJSON(nameList));
        }
        Assert.isTrue(!isPresent, "积木模板中变量描述重复!");
    }

    /**
     * 将素材上传新华智云
     *
     * @param templateVar
     * @param videoList
     * @return
     */
    private void uploadMedia(VideoTaskJobPO taskJob, TemplateVarBO templateVar,
            List<DigitalManVideoGenResultDTO> videoList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(videoList), "数字人视频素材上传智云入参videoList为空");
        Map<String, String> mediaProduceUrlMap = videoList.stream()
                .collect(Collectors.toMap((x) -> x.getMediaJobId().toString(), (x) -> x.getMediaUrl()));
        Map<String, Future<String>> futureMap = Maps.newLinkedHashMap();
        templateVar.getTemplateVariables().stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables()))
                .forEach(unit -> unit.getVariables().stream().filter(x -> isUndo(x.getReplaceType()))
                        .filter(x -> StringUtils.endsWithIgnoreCase(x.getKey(), Const.MAIN_VIDEO_SUFFIX)).forEach(x -> {
                            String mediaJobId = cleanStr(String.valueOf(x.getValue()));
                            String videoUrl = mediaProduceUrlMap.get(mediaJobId);
                            if (StringUtils.isNotBlank(videoUrl)) {
                                futureMap.put(x.getKey(), ttsCloneVoiceTaskExecutor.submit(() -> {
                                    JimuResult<MediaInfo> result = jimuService.addMedia(videoUrl, Const.JIMU_ASR_TAG);
                                    if (!result.getSuccess() || Objects.isNull(result.getData()) || StringUtils
                                            .isBlank(result.getData().getMediaId())) {
                                        log.error("数字人视频文件上传失败！myWorkBizId={},param={},resp={}",
                                                taskJob.getWorksBizId(), mediaJobId, JSONUtil.toJsonStr(result));
                                        throw BusinessServiceException.getInstance(
                                                String.format("视频合成任务生成异常，上传素材失败mediaJobId[%s]", mediaJobId));
                                    }
                                    return result.getData().getMediaId();
                                }));
                            }
                        }));
        HashMap<String, String> resultMap = Maps.newHashMap();
        futureMap.entrySet().stream().forEach(entry -> {
            try {
                resultMap.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                throw BusinessServiceException
                        .getInstance(Objects.nonNull(e.getCause()) ? e.getCause().getMessage() : e.getMessage());
            }
        });
        // 字幕校验
        templateVar.getTemplateVariables().stream().filter(unit -> CollectionUtils.isNotEmpty(unit.getVariables()))
                .forEach(unit -> unit.getVariables().stream().filter(x -> resultMap.containsKey(x.getKey()))
                        .forEach(x -> {
                            String value = String.valueOf(x.getTts());
                            String mediaId = resultMap.get(x.getKey());
                            x.setValue(mediaId);
                            checkSubtitle(mediaId, value, Const.ZERO, taskJob.getJobId());
                        }));
    }

    /**
     * 强制校验字幕信息
     *
     * @param mediaId
     * @param originalScript
     * @param count
     */
    private void checkSubtitle(String mediaId, String originalScript, int count, Long videoTaskJobId) {
        try {
            //循环去查询字幕生成结果，因需要同步查询，只能睡眠此线程
            Thread.sleep(THREAD_SLEEP_TIMES);
        } catch (InterruptedException e) {
            log.error("睡眠异常！mediaId={},videoTaskJobId={}", mediaId, videoTaskJobId);
            throw BusinessServiceException.getInstance("视频合成任务生成异常，睡眠异常");
        }
        JimuResult<MediaAsrListInfo> mediaInfo = jimuService.getAsrResult(mediaId);
        if (!mediaInfo.getSuccess() || Objects.isNull(mediaInfo.getData())) {
            log.error("查询字幕识别结果失败！mediaId={},videoTaskJobId={},resp={}", mediaId, videoTaskJobId,
                    JSONUtil.toJsonStr(mediaInfo));
            throw BusinessServiceException.getInstance("视频合成任务生成异常，字幕校验结果失败");
        }
        if (mediaInfo.getData().getStatus().equals(Const.ONE)) {
            ReviseAsrRequestDTO reviseAsrRequest = new ReviseAsrRequestDTO();
            reviseAsrRequest.setSubtitles(mediaInfo.getData().getTagExtDTOList().stream().map(tag -> {
                AsrSubtitleDTO vaAsrSubtitle = new AsrSubtitleDTO();
                vaAsrSubtitle.setSubtitle(tag.getTagName());
                vaAsrSubtitle.setTimePointStart(tag.getTimePointStart());
                vaAsrSubtitle.setTimePointEnd(tag.getTimePointEnd());
                return vaAsrSubtitle;
            }).collect(Collectors.toList()));
            reviseAsrRequest.setOriginalScript(originalScript);
            RevisedAsrResponseDTO revisedAsrResp = subtitleManager.asrRevise(reviseAsrRequest);
            MediaAsrListInfo mediaAsrListInfo = new MediaAsrListInfo();
            mediaAsrListInfo.setMediaId(mediaId);
            mediaAsrListInfo.setTagExtDTOList(revisedAsrResp.getRevisedAsrSubtitles().stream().map(mediaTagDTO -> {
                MediaAsrInfo mediaTagBO = new MediaAsrInfo();
                mediaTagBO.setTimePointEnd(mediaTagDTO.getTimePointEnd());
                mediaTagBO.setTimePointStart(mediaTagDTO.getTimePointStart());
                mediaTagBO.setTagName(mediaTagDTO.getRevisedSubtitle());
                return mediaTagBO;
            }).collect(Collectors.toList()));
            JimuResult<Boolean> asrUpdateResp = jimuService.asrUpdate(mediaAsrListInfo);
            if (!asrUpdateResp.getSuccess() || Objects.isNull(asrUpdateResp.getData())) {
                log.error("更新字幕失败！mediaId={},videoTaskJobId={},resp={}", mediaId, videoTaskJobId,
                        JSONUtil.toJsonStr(asrUpdateResp));
                throw BusinessServiceException.getInstance("视频合成任务生成异常，字幕校验结果失败");
            }
        } else {
            ++count;
            if (count > 20) {
                log.error("查询字幕生成时间过久！mediaId={},videoTaskJobId={},count={}", mediaId, videoTaskJobId, count);
                throw BusinessServiceException.getInstance("视频合成任务生成异常，字幕校验结果失败");
            }
            checkSubtitle(mediaId, originalScript, count, videoTaskJobId);
        }
    }
}
