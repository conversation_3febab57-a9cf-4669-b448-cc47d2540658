package com.dl.aiservice.share.videoproduce;

import com.dl.aiservice.share.videoproduce.dto.aliyun.SubmitMediaProducingJobDTO;
import com.dl.aiservice.share.videoproduce.dto.jimu.TemplateVarDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class VideoProduceParamDTO implements Serializable {
    private static final long serialVersionUID = -6442816271939461817L;

    @ApiModelProperty(value = "新华智云模板参数,sourceType为新华智云时必填", required = true)
    private TemplateVarDTO templateVar;

    @ApiModelProperty(value = "阿里云视频生成参数,sourceType为阿里云时必填", required = true)
    private SubmitMediaProducingJobDTO submitMediaProducingJob;

    @ApiModelProperty(value = "上层业务作品唯一标识")
    private Long worksBizId;

    @ApiModelProperty(value = "快视频合成整体任务系统内部唯一标识")
    private Long videoTaskJobId;

    @ApiModelProperty(value = "租户编码", required = true)
    @NotNull(message = "tenantCode必填")
    private String tenantCode;
}
