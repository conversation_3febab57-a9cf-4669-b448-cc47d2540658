package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.HuaWeiDigitalManJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:53
 */
@Component
public class HuaWeiDigitalManTask {

    @Resource
    private HuaWeiDigitalManJobHandler huaWeiDigitalManJobHandler;

    @XxlJob("huaweiDmVideoJob")
    public void huaweiDmVideoJob() {
        huaWeiDigitalManJobHandler.handleDmVideoCreate();
    }
}
