package com.dl.aiservice.biz.client.jimu;

import com.dl.aiservice.biz.client.jimu.dto.JimuExtInfo;
import com.dl.aiservice.biz.client.jimu.dto.ValueFormat;
import com.dl.aiservice.biz.client.jimu.dto.VarInfo;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.util.Objects;

public class VarInfoDeserializer extends StdDeserializer<VarInfo> {

    private static final String VIZZ_TYPE = "vizz_old";

    public VarInfoDeserializer(Class<?> vc) {
        super(vc);
    }

    /**
     * 不能删除，该默认构造方法删除之后会异常
     */
    public VarInfoDeserializer() {
        this(null);
    }

    @Override
    public VarInfo deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);
        VarInfo var = new VarInfo();
        String key = node.get("key").textValue();
        String name = node.get("name").textValue();
        JsonNode typeNode = node.get("type");
        // 新华智云侧bug，暂时默认值处理一下
        String type = VIZZ_TYPE;
        if (Objects.nonNull(typeNode)) {
            type = typeNode.textValue();
        }
        JsonNode extInfo = node.get("extInfo");
        if (extInfo != null) {
            JimuExtInfo digitalManInfo = new JimuExtInfo();
            JsonNode avatar = extInfo.get("avatar");
            JsonNode per = extInfo.get("per");
            digitalManInfo.setAvatar(avatar != null ? avatar.asText() : null);
            digitalManInfo.setPer(per != null ? per.asText() : null);
            var.setExtInfo(digitalManInfo);
        }
        var.setKey(key);
        var.setName(name);
        var.setType(type);

        JsonNode dataFormat = node.get("dataFormat");
        if (dataFormat != null) {
            if (dataFormat.isObject()) {
                JsonNode value = dataFormat.get("value");
                if (Objects.nonNull(value) && value.isTextual()) {
                    var.setDataFormat(new ValueFormat(value.textValue()));
                }
            }
            if (var.getDataFormat() == null) {
                var.setDataFormat(dataFormat);
            }
        }
        return var;
    }
}

