package com.dl.aiservice.biz.client.jimu.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JimuExtInfo {

    /**
     * 数字人形象代码
     */
    String avatar;

    /**
     * 合成音代码
     */
    String per;

    /**
     * 是否开启字幕显示 true 、false
     */
    Boolean showSubtitle;
}
