package com.dl.aiservice.biz.manager.subtitle;

import org.nlpcn.commons.lang.tire.domain.Forest;
import org.nlpcn.commons.lang.tire.library.Library;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;

/**
 * ansj_seg 用户词典加载器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-28 15:55
 */
@Component
public class AnsjSegUserLibraryLoader implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(AnsjSegUserLibraryLoader.class);

    /**
     * ansj_seg的用户词典url
     */
    @Value("${dl.subtitle.ansjseg.userLibraryUrl}")
    private String userLibraryUrl;

    private Forest forest = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            org.springframework.core.io.Resource resource = new UrlResource(userLibraryUrl);
            InputStream fis = resource.getInputStream();
            Forest forest = Library.makeForest(fis);
            this.forest = forest;
            LOGGER.info("ansj_seg设置用户词典成功");
        } catch (Exception e) {
            LOGGER.warn("ansj_seg设置用户词典失败! userLibraryUrl:{},e:", userLibraryUrl, e);
        }
    }

    public Forest getForest() {
        return forest;
    }
}
