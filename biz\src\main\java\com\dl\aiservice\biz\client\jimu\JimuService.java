package com.dl.aiservice.biz.client.jimu;

import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.client.jimu.dto.MediaAsrListInfo;
import com.dl.aiservice.biz.client.jimu.dto.MediaInfo;
import com.dl.aiservice.biz.client.jimu.dto.TemplateVarInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 新华智云-积木视频合成接口
 */
@Component
@Slf4j
public class JimuService {

    @Resource
    private JimuWebClient jimuWebClient;

    private static <T> JimuResult<T> resolveNullSuccess(JimuResult<T> result) {
        if (result != null && result.getSuccess() == null) {
            result.setSuccess(false);
        }
        return result;
    }

    public JimuResult<TemplateVarInfo> getVariables(String templateId) {
        return resolveNullSuccess(jimuWebClient.getVariables(templateId));
    }

    public JimuResult<MediaInfo> addMedia(String url, String taskCode) {
        return resolveNullSuccess(jimuWebClient.addMedia(url, taskCode));
    }

    public JimuResult<MediaAsrListInfo> getAsrResult(String mediaId) {
        return resolveNullSuccess(jimuWebClient.getAsrResult(mediaId));
    }

    public JimuResult<Boolean> asrUpdate(MediaAsrListInfo mediaAsrInfo) {
        return resolveNullSuccess(jimuWebClient.asrUpdate(mediaAsrInfo));
    }
}
