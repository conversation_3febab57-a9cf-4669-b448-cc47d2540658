package com.dl.aiservice.biz.manager.videoproduce.aliyun.impl;

import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.videoproduce.aliyun.AliyunVideoProduceHandleManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.videoproduce.dto.aliyun.OutputMediaConfigDTO;
import com.dl.aiservice.share.videoproduce.dto.aliyun.SubmitMediaProducingJobDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class AliyunVideoProduceHandleManagerImpl implements AliyunVideoProduceHandleManager {

    private Client client;

    @Resource
    private AiConfig aiConfig;

    private static final String NOTIFY_URL = "/video/produce/notify/aliyun";

    @PostConstruct
    private void createClient() throws Exception {
        Config config = new Config();
        config.accessKeyId = "LTAI5tRxgdkjVFJLyTEoc3o2";
        config.accessKeySecret = "******************************";
        config.endpoint = "ice.cn-shanghai.aliyuncs.com";
        config.regionId = "cn-shanghai";
        this.client = new Client(config);
    }

    @Override
    public ResultModel<VideoProduceResponseDTO> produce(VideoProduceParamDTO paramDTO) {
        SubmitMediaProducingJobDTO submitMediaProducingJobBO = new SubmitMediaProducingJobDTO();
        OutputMediaConfigDTO outputMediaConfig = new OutputMediaConfigDTO();
        outputMediaConfig.setMediaUrl(paramDTO.getSubmitMediaProducingJob().getOutputMediaConfig().getMediaUrl());
        outputMediaConfig.setBitrate(paramDTO.getSubmitMediaProducingJob().getOutputMediaConfig().getBitrate());
        outputMediaConfig.setHeight(paramDTO.getSubmitMediaProducingJob().getOutputMediaConfig().getHeight());
        outputMediaConfig.setWeight(paramDTO.getSubmitMediaProducingJob().getOutputMediaConfig().getWeight());
        submitMediaProducingJobBO.setOutputMediaConfig(outputMediaConfig);
        submitMediaProducingJobBO.setTimeline(paramDTO.getSubmitMediaProducingJob().getTimeline());
        VideoProduceResponseDTO videoProduceResponseDTO = new VideoProduceResponseDTO();
        try {
            videoProduceResponseDTO.setJobId(submitMediaProducingJob(paramDTO, submitMediaProducingJobBO));
            return ResultModel.success(videoProduceResponseDTO);
        } catch (BusinessServiceException businessServiceException) {
            return ResultModel.failure(businessServiceException.getErrCode(), businessServiceException.getMessage());
        }
    }

    @Override
    public ServiceChannelEnum getEnum() {
        return ServiceChannelEnum.ALIYUN;
    }

    public String submitMediaProducingJob(VideoProduceParamDTO paramDTO, SubmitMediaProducingJobDTO submitMediaProducingJobBO) {
        SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
        submitMediaProducingJobRequest.setTimeline(JsonUtils.toJSON(submitMediaProducingJobBO.getTimeline()));
        submitMediaProducingJobRequest.setOutputMediaConfig(JsonUtils.toJSON(submitMediaProducingJobBO.getOutputMediaConfig()));
        String notifyUrl = aiConfig.getCallbackPrefix() + NOTIFY_URL;
        submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\"" + notifyUrl + "\"}");
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            SubmitMediaProducingJobResponse response = client.submitMediaProducingJobWithOptions(submitMediaProducingJobRequest, runtime);
            log.info("AliyunVideoProduceHandleManager.submitMediaProducingJobWithOptions: request={},response={}", JsonUtils.toJSON(submitMediaProducingJobRequest), JsonUtils.toJSON(response));
            saveLog(paramDTO, submitMediaProducingJobRequest, response, null);
            return response.getBody().getJobId();
        } catch (Exception e) {
            log.debug("AliyunVideoProduceHandleManager.submitMediaProducingJobWithOptions 异常，request={}", JsonUtils.toJSON(submitMediaProducingJobRequest));
            log.error("AliyunVideoProduceHandleManager.submitMediaProducingJobWithOptions 异常", e);
            saveLog(paramDTO, submitMediaProducingJobRequest, null, e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }

    /**
     * 增加媒体生成记录表
     */
    private void saveLog(VideoProduceParamDTO paramDTO, SubmitMediaProducingJobRequest request,
                         SubmitMediaProducingJobResponse response, Exception e) {
        MediaProduceJobManager manager = ApplicationContextUtils.getContext().getBean(MediaProduceJobManager.class);
        HostTimeIdg hostTimeIdg = ApplicationContextUtils.getContext().getBean(HostTimeIdg.class);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(paramDTO.getTenantCode());
        job.setWorksBizId(paramDTO.getWorksBizId());
        job.setCallbackUrl(aiConfig.getCallbackPrefix() + NOTIFY_URL);
        job.setChannel(ServiceChannelEnum.ALIYUN.getCode());

        job.setJobType(Const.ZERO);
        job.setJobContent(JsonUtils.toJSON(request));
        if (Objects.nonNull(response)) {
            if (Objects.nonNull(response.getBody()) && Objects.nonNull(response.getBody().getJobId())) {
                job.setExtJobId(response.getBody().getJobId());
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(Const.ONE);
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
            }
        } else {
            job.setStatus(-Const.ONE);
            job.setFailCode("");
            job.setFailReason(e.getMessage());
        }
        manager.save(job);
    }
}
