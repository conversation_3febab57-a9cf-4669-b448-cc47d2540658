package com.dl.aiservice.biz.manager.videoproduce.jimu.impl;

import com.dl.aiservice.biz.client.jimu.JimuWebClient;
import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.manager.videoproduce.jimu.JimuVideoProduceHandleManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class JimuVideoProduceHandleManagerImpl implements JimuVideoProduceHandleManager {

    @Resource
    private JimuWebClient jimuProduceClient;
    @Resource
    private AiConfig aiConfig;

    private static final String JIMU_NOTIFY_URL = "/video/produce/notify/jimu";

    @Override
    public ResultModel<VideoProduceResponseDTO> produce(VideoProduceParamDTO paramDTO) {
        String bizCallBackUrl = paramDTO.getTemplateVar().getCallbackUrl();
        //设为ai-service的回调地址
        paramDTO.getTemplateVar().setCallbackUrl(aiConfig.getCallbackPrefix() + JIMU_NOTIFY_URL);
        Map<String, Object> headerMap = Maps.newHashMap();
        headerMap.put(JimuWebClient.HEADER_MEDIA_BIZ_ID, paramDTO.getWorksBizId());
        headerMap.put(JimuWebClient.HEADER_TENANT_CODE, paramDTO.getTenantCode());
        headerMap.put(JimuWebClient.HEADER_TASK_JOB_ID, paramDTO.getVideoTaskJobId());
        JimuResult<String> produceResult =
                jimuProduceClient.produce(headerMap, paramDTO.getTemplateVar(), bizCallBackUrl);
        JimuResult<String> result = resolveNullSuccess(produceResult);
        VideoProduceResponseDTO videoProduceResponseDTO = new VideoProduceResponseDTO();
        if (!result.getSuccess()) {
            return ResultModel.error(result.getCode(), result.getMessage());
        }
        videoProduceResponseDTO.setJobId(result.getData());
        videoProduceResponseDTO.setRequestId(result.getRequestId());
        return ResultModel.success(videoProduceResponseDTO);
    }

    @Override
    public ServiceChannelEnum getEnum() {
        return ServiceChannelEnum.XHZY;
    }

    private static <T> JimuResult<T> resolveNullSuccess(JimuResult<T> result) {
        if (result != null && result.getSuccess() == null) {
            result.setSuccess(false);
        }
        return result;
    }
}
