package com.dl.aiservice.biz.manager.voiceclone.huawei.impl;

import com.dl.aiservice.biz.client.huawei.HuaweiDigitalClient;
import com.dl.aiservice.biz.client.huawei.req.HuaweiTtsRequest;
import com.dl.aiservice.biz.client.huawei.resp.HuaweiTtsResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.voiceclone.ifly.IflyHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class HuaweiHandlerManagerImpl implements IflyHandlerManager {

    @Resource
    private HuaweiDigitalClient huaweiDigitalClient;
    @Value("${digtal.huawei.projectId}")
    private String projectId;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {

        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");

        HuaweiTtsRequest huaweiTtsRequest = buildTTSRequest(request);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(request.getWorksBizId());
        job.setChannel(channelUtil.getChannel());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(huaweiTtsRequest));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);
        MediaProduceJobPO updateJob = new MediaProduceJobPO();
        updateJob.setId(job.getId());
        try {
            HuaweiTtsResponse huaweiTtsResponse = huaweiDigitalClient.cloneTts(projectId, huaweiTtsRequest);
            if (Objects.nonNull(huaweiTtsResponse.getJob()) && Objects.nonNull(huaweiTtsResponse.getJob().getInferenceStatus())
                    && Objects.equals(huaweiTtsResponse.getJob().getInferenceStatus(), "")) {
                updateJob.setResponseDt(new Date());
                updateJob.setStatus(Const.ZERO);
                updateJob.setMediaUrl(huaweiTtsResponse.getJob().getCloneAudioResult());
                updateJob.setExtJobId(huaweiTtsResponse.getJob().getId());
                mediaProduceJobManager.updateById(updateJob);
                TTSResponseDTO result = new TTSResponseDTO();
                result.setMediaJobId(job.getMediaJobId());
                result.setAudioUrl(huaweiTtsResponse.getJob().getCloneAudioResult());
                //                    result.setDuration(0);
                return ResultModel.success(result);
            }
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            updateJob.setStatus(-Const.ONE);
            // 失败原因
            updateJob.setFailReason(huaweiTtsResponse.getJob().getErrorMessage());
            updateJob.setExtJobId(huaweiTtsResponse.getJob().getId());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            return ResultModel.error("-1", "语音合成失败:" + huaweiTtsResponse.getJob().getErrorMessage());
        } catch (Exception e) {
            log.error("语音合成失败", e);
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private HuaweiTtsRequest buildTTSRequest(TTSProduceParamDTO request) {
        return HuaweiTtsRequest.builder()
                .name(hostTimeIdg.generateId().toString())
                .trainingJobId(request.getVoiceName())
                .userInputText(request.getText())
                .config(HuaweiTtsRequest.HuaweiTtsConfig.builder()
                        .speed(StringUtils.isNotBlank(request.getSpeed()) ? Integer.valueOf(request.getSpeed()) : null)
                        .volume(StringUtils.isNotBlank(request.getVolume()) ? Integer.valueOf(request.getVoiceName()) : null)
                        .pitch(StringUtils.isNotBlank(request.getPitch()) ? Integer.valueOf(request.getPitch()) : null)
                        .build())
                .build();
    }


    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.HUAWEI);
    }
}