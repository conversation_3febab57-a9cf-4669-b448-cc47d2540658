package com.dl.aiservice.biz.client.jimu.intecepter;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.client.jimu.JimuWebClient;
import com.dl.aiservice.biz.client.jimu.dto.JimuResult;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.common.util.ForestLogUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.dto.jimu.JimuProperties;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-13 19:38
 */
@Slf4j
public class JimuProduceInterceptor implements Interceptor {

    private static final String RESP_CODE = "code";
    private static final String SIGN_VERSION = "sign_version";
    private static final String SIGN_NONCE = "sign_nonce";
    private static final String SIGN_TYPE = "sign_type";
    private static final String TIMESTAMP = "timestamp";
    private static final String ACCESS_KEY = "access_key";
    private static final String SIGNATURE = "signature";

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        JimuProperties properties = ApplicationContextUtils.getContext().getBean(JimuProperties.class);
        String ak = properties.getAccessKey();
        String sk = properties.getSecretKey();
        //生成签名并添加到request里
        sign(ak, sk, null, request);
        return Boolean.TRUE;
    }

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        Interceptor.super.onSuccess(data, request, response);
        videoProcess(data, request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        videoProcess(ex, request, response);
        throw BusinessServiceException.getInstance(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString(), ex.getMessage());
    }

    private void videoProcess(Object data, ForestRequest request, ForestResponse response) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (!StringUtils.equals(url, JimuWebClient.JIMU_PRODUCE_URL)) {
            return;
        }
        MediaProduceJobManager manager = ApplicationContextUtils.getContext().getBean(MediaProduceJobManager.class);
        HostTimeIdg hostTimeIdg = ApplicationContextUtils.getContext().getBean(HostTimeIdg.class);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(request.getHeader(JimuWebClient.HEADER_TENANT_CODE).getValue());
        String value = request.getHeader(JimuWebClient.HEADER_MEDIA_BIZ_ID).getValue();
        if (NumberUtils.isNumber(value)) {
            job.setWorksBizId(Long.valueOf(value));
        }
        String tJobId = request.getHeader(JimuWebClient.HEADER_TASK_JOB_ID).getValue();
        if (NumberUtils.isNumber(tJobId)) {
            job.setVideoTaskJobId(Long.valueOf(tJobId));
        }
        String bizCallbackUrl = (String) request.getArgument(Const.TWO);
        job.setCallbackUrl(bizCallbackUrl);
        job.setChannel(ServiceChannelEnum.XHZY.getCode());
        job.setJobType(Const.ZERO);
        job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        if (data instanceof JimuResult) {
            JimuResult resp = (JimuResult) data;
            job.setExtJobId((String) resp.getData());
            if (resp.getSuccess()) {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(Const.ONE);
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                // 三方错误码
                job.setFailCode(resp.getCode());
                // 失败原因
                job.setFailReason(response.getContent());
            }
        } else {
            job.setStatus(-Const.ONE);
            JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
            job.setFailCode(responseCnt.getStr(RESP_CODE));
            job.setFailReason(response.getContent());
        }
        manager.save(job);
    }

    private static void sign(String accessKey, String secretKey, Map<String, String> bizParam, ForestRequest request) {
        if (Objects.isNull(bizParam) || bizParam.size() == 0) {
            bizParam = new HashMap<>();
        }
        if (Objects.isNull(bizParam.get(SIGN_VERSION))) {
            bizParam.put(SIGN_VERSION, "2.0");
        }
        if (Objects.isNull(bizParam.get(SIGN_NONCE))) {
            bizParam.put(SIGN_NONCE, UUID.randomUUID().toString().replace(SymbolE.MINUS.getValue(), StringUtils.EMPTY));
        }
        if (Objects.isNull(bizParam.get(SIGN_TYPE))) {
            bizParam.put(SIGN_TYPE, "MD5");
        }
        String currentTime = String.valueOf(System.currentTimeMillis());
        if (Objects.isNull(bizParam.get(TIMESTAMP))) {
            bizParam.put(TIMESTAMP, currentTime);
        }
        //添加access_key
        bizParam.put(ACCESS_KEY, accessKey);
        //排序后的参数字符串
        String sortParam = sortParamToString(bizParam);
        //MD5加密环节
        String sign = getSignature(accessKey, secretKey, currentTime, sortParam);
        request.addQuery(bizParam);
        request.addQuery(SIGNATURE, sign);
    }

    private static String sortParamToString(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(params) && params.size() > Const.ZERO) {
            params.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(paramEntry -> {
                sb.append(paramEntry.getKey())
                        .append(SymbolE.EQUAL.getValue())
                        .append(paramEntry.getValue())
                        .append(SymbolE.WELL_NUMBER.getValue());
            });
        }
        String res = sb.toString();
        return res;
    }

    /**
     * 获取签名串
     *
     * @param accessKey ak
     * @param secretKey sk
     * @param timestamp 时间戳
     * @param param     字典序的参数(例如:access_key=JKICxSiPR6qXpiPs&status=test&state=bobo188&timestamp=1571744445945)
     * @return 签名串
     */
    private static String getSignature(String accessKey, String secretKey, String timestamp, String param) {
        if (StringUtils.isAnyBlank(accessKey, secretKey, timestamp)) {
            log.error("鉴权参数不可为空, accessKey={},secretKey={},timestamp={}", accessKey, secretKey, timestamp);
            throw new IllegalArgumentException();
        }
        String needSignatureStr = secretKey + "$" + timestamp + "$" + accessKey + "$" + param;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(needSignatureStr.getBytes("UTF-8"));
            byte[] summery = md5.digest();
            StringBuilder md5StrBuilder = new StringBuilder();
            for (byte aSummery : summery) {
                if (Integer.toHexString(255 & aSummery).length() == 1) {
                    md5StrBuilder.append("0").append(Integer.toHexString(255 & aSummery));
                } else {
                    md5StrBuilder.append(Integer.toHexString(255 & aSummery));
                }
            }
            return md5StrBuilder.toString();
        } catch (Exception e) {
            log.error("获取签名串失败", e);
            return StringUtils.EMPTY;
        }
    }

}
