package com.dl.aiservice.web.controller.videomake;

import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.dal.po.VideoTaskJobPO;
import com.dl.aiservice.biz.manager.VideoTaskJobManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManScenesManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManAndVoiceBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceBO;
import com.dl.aiservice.biz.manager.enums.VideoModeEnum;
import com.dl.aiservice.biz.service.videomake.VideoMakeService;
import com.dl.aiservice.biz.service.videomake.VideoMakeServiceHelper;
import com.dl.aiservice.share.videomake.VideoMakeParamDTO;
import com.dl.aiservice.share.videomake.virtualman.VirtualManInfoDTO;
import com.dl.aiservice.share.videomake.virtualvoice.VirtualVoiceInfoDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * @ClassName VideoMakeController
 * @Description 快视频合成请求处理类
 * <AUTHOR>
 * @Date 2023/4/13 16:22
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/video/make")
public class VideoMakeController {

    private static final String DEFAULT_DL = "DEFAULT";

    @Resource
    private DaVirtualManScenesManager daVirtualManScenesManager;
    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private VideoMakeServiceHelper videoMakeServiceHelper;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private VideoTaskJobManager videoTaskJobManager;
    @Resource
    private ExecutorService syncVideoGenExecutor;

    /**
     * 视频合成
     *
     * @param param
     * @return 返回 内部jobId
     */
    @ApiOperation("视频合成")
    @PostMapping
    ResultModel<String> make(@Validated @RequestBody VideoMakeParamDTO param) {
        log.info("/video/make请求入参param={}", JsonUtils.toJSON(param));
        VideoModeEnum videoMode = VideoModeEnum.getEnum(param.getVideoMode());
        checkVideoMode(videoMode);
        DaVirtualManAndVoiceBO daVirtualManAndVoice =
                checkAndGetDigitalAssetInfo(videoMode, param.getVirtualManInfo(), param.getVirtualVoiceInfo());
        VideoMakeService videoMakeService = videoMakeServiceHelper.get(param.getMediaProduceChannel());
        Assert.notNull(videoMakeService, "暂不支持该视频合成商");
        Long videoTaskJobId = saveVideoTaskJob(param);
        String tenantCode = channelUtil.getTenantCode();
        syncVideoGenExecutor.execute(
                () -> videoMakeService.doMake(tenantCode, param.getMediaProduceChannel(), param.getMessage(),
                        daVirtualManAndVoice, param.getWorksBizId(), videoTaskJobId));
        return ResultModel.success(videoTaskJobId.toString());
    }

    private Long saveVideoTaskJob(VideoMakeParamDTO param) {
        VideoTaskJobPO videoTaskJob = new VideoTaskJobPO();
        videoTaskJob.setJobId(hostTimeIdg.generateId().longValue());
        videoTaskJob.setWorksBizId(param.getWorksBizId());
        videoTaskJob.setCallbackUrl(param.getMessage().getCallbackUrl());
        videoTaskJob.setTenantCode(channelUtil.getTenantCode());
        videoTaskJobManager.save(videoTaskJob);
        return videoTaskJob.getJobId();
    }

    private void checkVideoMode(VideoModeEnum videoMode) {
        Assert.notNull(videoMode, "请选择正确的录制模式");
        Assert.isTrue(VideoModeEnum.IMITATION_HUMAN == videoMode || VideoModeEnum.IMAGE_ONLY == videoMode,
                "当前仅支持数字分身、照片版两种录制模式");
    }

    /**
     * 根据报文内容对数字分身及数字声音信息做非空校验<br/>
     * 并根据需要获取数字分身、数字声音信息
     *
     * @param videoMode
     * @param virtualManInfo
     * @param virtualVoiceInfo
     * @return
     */
    private DaVirtualManAndVoiceBO checkAndGetDigitalAssetInfo(VideoModeEnum videoMode,
            VirtualManInfoDTO virtualManInfo, VirtualVoiceInfoDTO virtualVoiceInfo) {
        DaVirtualManAndVoiceBO result = new DaVirtualManAndVoiceBO();
        // 判断是否需要数字人信息
        boolean vmSceneIdNonNull = Objects.nonNull(virtualManInfo) && Objects.nonNull(virtualManInfo.getVmBizId())
                && StringUtils.isNotBlank(virtualManInfo.getVmSceneBizId());
        boolean voiceBizIdNonNull =
                Objects.nonNull(virtualVoiceInfo) && Objects.nonNull(virtualVoiceInfo.getVoiceBizId());
        // 数字分身场景
        if (VideoModeEnum.IMITATION_HUMAN == videoMode) {
            Assert.isTrue(vmSceneIdNonNull && voiceBizIdNonNull, "数字分身及声音入参信息必填");
            List<DaVirtualManScenesBO> virtualManSceneList =
                    daVirtualManScenesManager.listVirtualManScene(channelUtil.getTenantCode(),
                            virtualManInfo.getVmBizId(), null);
            Assert.notEmpty(virtualManSceneList, "数字分身不存在");
            DaVirtualManScenesBO daVirtualManSceneInfo = virtualManSceneList.stream()
                    .filter(element -> StringUtils.equals(element.getSceneId(), virtualManInfo.getVmSceneBizId()))
                    .findAny()
                    .orElse(null);
            Assert.notNull(daVirtualManSceneInfo, "数字分身场景信息不存在");
            Assert.isTrue(checkEffectDt(daVirtualManSceneInfo.getEffectDt()), "数字分身未生效");
            Assert.isTrue(checkExpiryDt(daVirtualManSceneInfo.getExpiryDt()), "数字分身已过期");
            result.setDaVirtualManScenes(daVirtualManSceneInfo);
            result.setDaVirtualVoice(getDaVirtualVoice(virtualVoiceInfo));
            return result;
        }
        // 判断是否需要数字声音信息
        Assert.isTrue(voiceBizIdNonNull, "数字声音入参信息必填");
        result.setDaVirtualVoice(getDaVirtualVoice(virtualVoiceInfo));
        return result;
    }

    private DaVirtualVoiceBO getDaVirtualVoice(VirtualVoiceInfoDTO virtualVoiceInfo) {
        DaVirtualVoicePO voiceInfo = getVirtualVoiceInfo(virtualVoiceInfo.getVoiceBizId());
        DaVirtualVoiceBO daVirtualVoice = new DaVirtualVoiceBO();
        daVirtualVoice.setBizId(voiceInfo.getBizId());
        daVirtualVoice.setVoiceKey(voiceInfo.getVoiceKey());
        daVirtualVoice.setVoiceType(voiceInfo.getVoiceType());
        daVirtualVoice.setChannel(voiceInfo.getChannel());
        daVirtualVoice.setEffectDt(voiceInfo.getEffectDt());
        daVirtualVoice.setExpiryDt(voiceInfo.getExpiryDt());
        daVirtualVoice.setGender(voiceInfo.getGender());
        daVirtualVoice.setPitch(virtualVoiceInfo.getPitch());
        daVirtualVoice.setVolume(virtualVoiceInfo.getVolume());
        daVirtualVoice.setSpeed(virtualVoiceInfo.getSpeed());
        daVirtualVoice.setEmotionCategory(virtualVoiceInfo.getEmotionCategory());
        daVirtualVoice.setEmotionIntensity(virtualVoiceInfo.getEmotionIntensity());
        return daVirtualVoice;
    }

    private boolean checkEffectDt(Date effectDt) {
        return Objects.isNull(effectDt) || effectDt.compareTo(new Date()) < Const.ZERO;
    }

    private boolean checkExpiryDt(Date expiryDt) {
        return Objects.isNull(expiryDt) || expiryDt.compareTo(new Date()) > Const.ZERO;
    }

    private DaVirtualVoicePO getVirtualVoiceInfo(Long voiceBizId) {
        DaVirtualVoicePO voiceInfo = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
                .eq(DaVirtualVoicePO::getIsEnabled, Const.ONE)
                .eq(DaVirtualVoicePO::getBizId, voiceBizId)
                .one();
        Assert.notNull(voiceInfo, "数字声音不存在");
        Assert.isTrue(checkEffectDt(voiceInfo.getEffectDt()), "数字声音未生效");
        Assert.isTrue(checkExpiryDt(voiceInfo.getExpiryDt()), "数字声音已过期");
        return voiceInfo;
    }
}
