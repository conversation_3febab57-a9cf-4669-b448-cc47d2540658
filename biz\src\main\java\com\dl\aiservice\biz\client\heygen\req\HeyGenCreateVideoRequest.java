package com.dl.aiservice.biz.client.heygen.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HeyGenCreateVideoRequest implements Serializable {

    /**
     * 整个视频的背景。可能是：
     * - 图像的公共 HTTP 链接 （jpg/jpeg/png）
     * - 视频的公共 HTTP 链接（仅限 mp4）。如果背景视频短于整个视频，背景将循环整个视频。
     * - 六位十六进制颜色代码以 # 开头。（例如#FAFAFA)
     */
    @JsonProperty(value = "background")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String background;

    /**
     * 视频的宽高比，应该是其中之一
     *  16:9
     *  9:16
     */
    @JsonProperty(value = "ratio")
    private String ratio;

    /**
     * 将测试标志设置为使用测试模式。在测试模式下生成不会花费您积分，并且会在视频上包含水印。
     */
    @JsonProperty(value = "test")
    private Boolean test;

    /**
     * 将其设置为 。版本现在是 alpha 版，如果有任何重大更改，API 将保留当前版本。v1alpha
     */
    @JsonProperty(value = "version")
    private String version ;

    /**
     * 将其设置为创建带字幕的视频。
     */
    @JsonProperty(value = "caption_open")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean captionOpen;

    /**
     * 剪辑列表表示视频的内容
     */
    @JsonProperty(value = "clips")
    private List<HeyGenCreateVideoClipRequest> clips;

}
