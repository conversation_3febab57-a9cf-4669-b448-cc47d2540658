package com.dl.aiservice.biz.client.heygen.interceptor;

import com.dl.framework.common.idg.HostTimeIdg;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

@Slf4j
public class HeyGenDigitalInterceptor implements Interceptor {

    final private static String ACCEPT = "accept";

    final private static String CONTENT_TYPE = "content-type";

    final private static String API_KEY = "X-API-KEY";


    @Override
    public boolean beforeExecute(ForestRequest request) {
        request.addHeader(ACCEPT,"application/json");
        request.addHeader(CONTENT_TYPE,"application/json");
        request.addHeader(API_KEY,"");
        return true;
    }
}
