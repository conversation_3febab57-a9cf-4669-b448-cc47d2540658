package com.dl.aiservice.biz.service.digital.ivh.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.ivh.IvhDigitalClient;
import com.dl.aiservice.biz.client.ivh.IvhDigitalSmallSampleClient;
import com.dl.aiservice.biz.client.ivh.IvhDigitalTrainClient;
import com.dl.aiservice.biz.client.ivh.enums.IvhVideoMakeEnum;
import com.dl.aiservice.biz.client.ivh.req.IvhAnchor;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetAnchorRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetResourceRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetServiceAssetRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetSmallSampleAnchorRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetTimbreRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhListenTtsRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhLogoParams;
import com.dl.aiservice.biz.client.ivh.req.IvhSpeedRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhTrainingCreateRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoAdvancedRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoMakeAdvancedRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoMakeRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhActionResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetAnchorImageResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetAnchorResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetSmallSampleAnchorResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhProgressResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhSentences;
import com.dl.aiservice.biz.client.ivh.resp.IvhServiceAssetResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhSmallSampleVirtualMan;
import com.dl.aiservice.biz.client.ivh.resp.IvhTask;
import com.dl.aiservice.biz.client.ivh.resp.IvhTimbreResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhTimbres;
import com.dl.aiservice.biz.client.ivh.resp.IvhTrainingCreateResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhUploadTrainResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhVirtualman;
import com.dl.aiservice.biz.client.ivh.resp.IvhVirtualmanImage;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.CosBean;
import com.dl.aiservice.biz.common.util.CosUtil;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.ivh.IvhGrammarTransformer;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.GetTimbreRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhTimbresDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhVideoMakeRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ivh.IvhActionsDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ivh.IvhTimbreActionResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.DigitalComposeEnum;
import com.dl.aiservice.biz.service.digital.enums.IvhSynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.ivh.IvhDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsResponseDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhSmallSampleAnchorResponseDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhSmallSampleVirtualManDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhTimbreDetailDTO;
import com.dl.aiservice.share.digitalman.ivh.SentencesDTO;
import com.dl.aiservice.share.digitalman.ivh.WordsDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicSessionCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: xuebin
 * @description 腾讯云数字人接口
 * @Date: 2023/2/28 17:50
 */
@Slf4j
@Service
public class IvhDigitalServiceImpl extends AbstractDigitalService implements IvhDigitalService {

    private static final String CALL_BACK_URL = "%s/ivh/callback";
    private static final String VIDEO = "video/";
    private static final String IDCARD = "idcard/";

    @Resource
    private IvhDigitalClient ivhDigitalClient;
    @Resource
    private IvhDigitalSmallSampleClient ivhDigitalSmallSampleClient;
    @Resource
    private IvhDigitalTrainClient ivhDigitalTrainClient;
    @Resource
    private AiConfig aiConfig;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private IvhGrammarTransformer ivhGrammarTransformer;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.IVH, ServiceChannelEnum.FUJIA_IVH, ServiceChannelEnum.DLSY_IVH);
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO createRequestDTO) {
        IvhVideoMakeAdvancedRequest request = convertCreate(createRequestDTO);
        IvhBaseResponse<IvhTask> resp = ivhDigitalClient
                .videoMakeAdvanced(IvhBaseRequest.<IvhVideoMakeAdvancedRequest>builder().payload(request).build(),
                        createRequestDTO.getUpdateId());
        CreateResponseDTO responseDTO = new CreateResponseDTO();
        if (resp.isSuccess()) {
            responseDTO.setTaskId(resp.getPayload().getTaskId());
        }
        return responseDTO;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        ResultPageModel<RobotResponseDTO> respDto = new ResultPageModel<>();
        IvhBaseResponse<IvhGetAnchorResponse> resp = ivhDigitalClient.getAnchor(
                IvhBaseRequest.<IvhGetAnchorRequest>builder()
                        .payload(IvhGetAnchorRequest.builder().getAllResource(Boolean.FALSE).build())
                        .build());
        if (resp.isSuccess()) {
            List<IvhVirtualman> virtualmans = resp.getPayload().getVirtualmanResources().get(0).getVirtualmans();
            List<RobotResponseDTO> responseDTOS = virtualmans.stream().map(virtualman -> {
                RobotResponseDTO robotResponseDTO = new RobotResponseDTO();
                robotResponseDTO.setId(virtualman.getAnchorCode());
                robotResponseDTO.setCoverUrl(virtualman.getHeaderImage());
                robotResponseDTO.setRobotName(virtualman.getAnchorName());
                BeanUtils.copyProperties(virtualman, robotResponseDTO);
                return robotResponseDTO;
            }).collect(Collectors.toList());

            respDto.setDataResult(responseDTOS);
        }
        return respDto;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO request) {
        IvhBaseResponse<IvhGetAnchorImageResponse> resp = ivhDigitalClient.getResourceByAnchor(
                IvhBaseRequest.<IvhGetResourceRequest>builder()
                        .payload(IvhGetResourceRequest.builder()
                                .getAllResource(true)
                                .anchorCode(request.getId())
                                .build())
                        .build());
        RobotDetailResponseDTO respDto = new RobotDetailResponseDTO();

        if (resp.isSuccess()) {
            List<IvhVirtualmanImage> virtualmans = resp.getPayload().getVirtualmanResources().get(0).getVirtualmans();
            List<RobotDetailDTO> list = virtualmans.stream().map(v -> {
                RobotDetailDTO robotDetailDTO = new RobotDetailDTO();
                BeanUtils.copyProperties(v, robotDetailDTO);
                robotDetailDTO.setSceneId(v.getVirtualmanKey());
                return robotDetailDTO;
            }).collect(Collectors.toList());

            respDto.setRobotDetailList(list);
        }
        return respDto;
    }

    private IvhVideoMakeAdvancedRequest convertCreate(CreateRequestDTO createRequestDTO) {
        IvhVideoMakeAdvancedRequest request = new IvhVideoMakeAdvancedRequest();
        String virtualManKey = createRequestDTO.getVirtualmanKey();
        if (StringUtils.isEmpty(virtualManKey)) {
            virtualManKey = createRequestDTO.getSceneId();
        }
        request.setVirtualmanKey(virtualManKey);
        request.setCallbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()));
        if (Objects.equals(createRequestDTO.getType(), DigitalComposeEnum.VIDEO.getCode())) {
            request.setDriverType(IvhVideoMakeEnum.ORIGINAL_VOICE.getDesc());
            request.setInputAudioUrl(createRequestDTO.getAudioUrl());
        } else if (Objects.equals(createRequestDTO.getType(), DigitalComposeEnum.TEXT.getCode())) {
            request.setInputSsml(ivhGrammarTransformer.grammarTransform(createRequestDTO.getText()));
        } else {
            throw BusinessServiceException.getInstance("腾讯云不支持合成");
        }
        IvhSpeedRequest ivhSpeedRequestDTO = IvhSpeedRequest.builder().build();
        Double speed = createRequestDTO.getSpeed();
        ivhSpeedRequestDTO.setSpeed(Objects.nonNull(speed) ? speed : Const.ONE.longValue());
        //清戈的声音暂时用陈超群的
        if (Objects.equals("d665a7029cd5487a802b7f9a8cf49456", virtualManKey)) {
            ivhSpeedRequestDTO.setTimbreKey("dl_chenchaoqun_dz");
        }
        if (Objects.equals("d34619ac7e0b45329055c69c247fae5d", virtualManKey)) {
            ivhSpeedRequestDTO.setTimbreKey("timbre_4565");
        }
        request.setSpeechParam(ivhSpeedRequestDTO);
        IvhVideoAdvancedRequest ivhVideoAdvancedRequest = new IvhVideoAdvancedRequest();
        BeanUtils.copyProperties(createRequestDTO, ivhVideoAdvancedRequest);
        if (StringUtils.isBlank(ivhVideoAdvancedRequest.getFormat())) {
            // 生成透明底视频
            ivhVideoAdvancedRequest.setFormat("TransparentWebm");
        }
        if (CollectionUtils.isNotEmpty(createRequestDTO.getLogoParams())) {
            List<IvhLogoParams> ivhLogoParamsList = createRequestDTO.getLogoParams().stream().map(ivhLogoParamsDTO -> {
                IvhLogoParams ivhLogoParams = new IvhLogoParams();
                BeanUtils.copyProperties(ivhLogoParamsDTO, ivhLogoParams);
                return ivhLogoParams;
            }).collect(Collectors.toList());
            ivhVideoAdvancedRequest.setLogoParams(ivhLogoParamsList);
        }
        IvhAnchor ivhAnchor = new IvhAnchor();
        if (Objects.nonNull(createRequestDTO.getAnchorParam())) {
            BeanUtils.copyProperties(createRequestDTO.getAnchorParam(), ivhAnchor);
            ivhVideoAdvancedRequest.setAnchorParam(ivhAnchor);
        }
        request.setVideoParam(ivhVideoAdvancedRequest);
        if (Objects.equals(createRequestDTO.getCustomStoreUrl(), Const.ONE)) {
            request.setVideoStorageS3Url(
                    cosFileUploadManager.generatePresignedUrl(hostTimeIdg.generateId().longValue() + ".webm",
                            HttpMethodName.PUT));
        }
        return request;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        IvhTask ivhTask = new IvhTask();
        ivhTask.setTaskId(gjTrainRequestDTO.getTaskId());
        IvhBaseResponse<IvhTrainingCreateResponse> resp =
                ivhDigitalTrainClient.getTrainingProgress(IvhBaseRequest.<IvhTask>builder().payload(ivhTask).build());
        TrainResponseDTO respDTO = new TrainResponseDTO();
        if (resp.isSuccess()) {
            BeanUtils.copyProperties(resp.getPayload(), respDTO);
        }
        return respDTO;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        IvhTask ivhTask = new IvhTask();
        ivhTask.setTaskId(taskRequestDTO.getTaskId());
        IvhBaseResponse<IvhProgressResponse> resp =
                ivhDigitalClient.getProgress(IvhBaseRequest.<IvhTask>builder().payload(ivhTask).build());
        ProgressResponseDTO respDTO = new ProgressResponseDTO();
        if (resp.isSuccess()) {
            IvhProgressResponse ivhProgressResponse = resp.getPayload();
            respDTO.setSynthesisStatus(IvhSynthesisStatusEnum.valueOf(ivhProgressResponse.getStatus()).getCode());
            respDTO.setVideoUrl(ivhProgressResponse.getMediaUrl());
            respDTO.setSrtUrl(ivhProgressResponse.getSubtitlesUrl());
            respDTO.setProgress(ivhProgressResponse.getProgress());
            respDTO.setArrayCount(ivhProgressResponse.getArrayCount());
            respDTO.setFailMessage(ivhProgressResponse.getFailMessage());
            if (Objects.nonNull(ivhProgressResponse.getDuration())) {
                BigDecimal durationDecimal = new BigDecimal(ivhProgressResponse.getDuration());
                respDTO.setDuration(durationDecimal.divide(new BigDecimal(1000)).toString());
            }
        }
        return respDTO;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        IvhTrainingCreateRequest ivhTrainingCreateRequest = IvhTrainingCreateRequest.builder().build();
        ivhTrainingCreateRequest.setAnchorName(request.getName());
        ivhTrainingCreateRequest.setMakeType("IMAGE");
        ivhTrainingCreateRequest.setIdentityCosUrl(uploadCOS(request.getIdentityCosUrl(), IDCARD));
        ivhTrainingCreateRequest.setMaterialCosUrl(uploadCOS(request.getIdentityCosUrl(), VIDEO));
        ivhTrainingCreateRequest.setIsHaveBackground(request.getIsHaveBackground());
        IvhBaseResponse<IvhTask> resp = ivhDigitalTrainClient.trainingMake(
                IvhBaseRequest.<IvhTrainingCreateRequest>builder().payload(ivhTrainingCreateRequest).build(),
                request.getUpdateId());
        CreateTrainingResponseDTO respDTO = new CreateTrainingResponseDTO();
        if (resp.isSuccess()) {
            respDTO.setTaskId(resp.getPayload().getTaskId());
        }
        return respDTO;

    }

    @Override
    public IvhTimbreActionResponseDTO getTimbreAction(GetTimbreRequestDTO request) {

        IvhBaseResponse<IvhActionResponse> actions = ivhDigitalClient.getActions(
                IvhBaseRequest.<IvhGetTimbreRequest>builder()
                        .payload(IvhGetTimbreRequest.builder().virtualmanKey(request.getVirtualmanKey()).build())
                        .build());

        IvhBaseResponse<IvhTimbreResponse> timbre = ivhDigitalClient.getTimbre(
                IvhBaseRequest.<IvhGetTimbreRequest>builder()
                        .payload(IvhGetTimbreRequest.builder().virtualmanKey(request.getVirtualmanKey()).build())
                        .build());
        IvhTimbreActionResponseDTO respDTO = new IvhTimbreActionResponseDTO();

        if (actions.isSuccess() && timbre.isSuccess()) {
            List<IvhActionsDTO> actionsDTOS = actions.getPayload().getActions().stream().map(action -> {
                IvhActionsDTO ivhActionsDTO = new IvhActionsDTO();
                BeanUtils.copyProperties(action, ivhActionsDTO);
                return ivhActionsDTO;
            }).collect(Collectors.toList());
            List<IvhTimbresDTO> timbreDTO = timbre.getPayload().getTimbres().stream().map(timbres -> {
                IvhTimbresDTO ivhActionsDTO = new IvhTimbresDTO();
                BeanUtils.copyProperties(timbres, ivhActionsDTO);
                return ivhActionsDTO;
            }).collect(Collectors.toList());
            respDTO.setTimbres(timbreDTO);
            respDTO.setActions(actionsDTOS);
        }
        return respDTO;
    }

    @Override
    public IvhListenTtsResponseDTO listenTts(IvhListenTtsRequestDTO request) {
        IvhListenTtsRequest ivhListenTtsRequest = IvhListenTtsRequest.builder().build();
        BeanUtils.copyProperties(request, ivhListenTtsRequest);
        IvhBaseResponse<IvhTask> ttsResp = ivhDigitalClient
                .listenTts(IvhBaseRequest.<IvhListenTtsRequest>builder().payload(ivhListenTtsRequest).build());
        log.info("调用腾讯云tts接口，入参:{},,,出参:{}", JSONUtil.toJsonStr(ivhListenTtsRequest), JSONUtil.toJsonStr(ttsResp));
        IvhListenTtsResponseDTO result = new IvhListenTtsResponseDTO();
        if (!ttsResp.isSuccess()) {
            result.setStatus(IvhSynthesisStatusEnum.FAIL.getDesc());
            result.setFailCode(ttsResp.getHeader().getCode() + "");
            result.setFailMessage(ttsResp.getHeader().getMessage());
            return result;
        }

        result.setIvhTaskId(ttsResp.getPayload().getTaskId());
        this.getProcessResult(result, ttsResp.getPayload().getTaskId(), Const.ZERO);
        return result;
    }

    private void getProcessResult(IvhListenTtsResponseDTO result, String taskId, int count) {
        //循环去查询字幕生成结果，因需要同步查询，只能睡眠此线程
        long l = System.currentTimeMillis();
        try {
            Thread.sleep(2000);
            log.info("getProcessResult>>taskId:{},,,count={},l={},time={}", taskId, count, l,
                    System.currentTimeMillis() - l);
        } catch (InterruptedException e) {
            log.error("睡眠异常！taskId={},,,e:{}", taskId, e);
            throw BusinessServiceException.getInstance("腾讯云获取tts合成任务异常，线程休眠异常");
        }
        IvhTask ivhTask = new IvhTask();
        ivhTask.setTaskId(taskId);
        IvhBaseResponse<IvhProgressResponse> resp =
                ivhDigitalClient.getProgress(IvhBaseRequest.<IvhTask>builder().payload(ivhTask).build());
        if (!resp.isSuccess() || Objects.isNull(resp.getPayload())) {
            log.error("查询合成任务结果失败！param={},resp={}", taskId, JSONUtil.toJsonStr(resp));
            throw BusinessServiceException.getInstance("腾讯云获取tts合成任务失败");
        }
        IvhProgressResponse payload = resp.getPayload();
        IvhSynthesisStatusEnum anEnum = IvhSynthesisStatusEnum.getEnum(payload.getStatus());
        if (IvhSynthesisStatusEnum.SUCCESS == anEnum) {
            IvhProgressResponse ivhProgressResponse = resp.getPayload();
            log.info("腾讯云tts合成成功，ivhProgressResponse:{}", JSONUtil.toJsonStr(ivhProgressResponse));
            result.setStatus(ivhProgressResponse.getStatus());
            result.setMediaUrl(ivhProgressResponse.getMediaUrl());
            result.setFailMessage(ivhProgressResponse.getFailMessage());
            result.setTextTimestampResult(
                    IvhDigitalServiceImpl.cnvTextTimestampResult(ivhProgressResponse.getTextTimestampResult()));
            return;
        }
        if (IvhSynthesisStatusEnum.FAIL == anEnum) {
            IvhProgressResponse ivhProgressResponse = resp.getPayload();
            result.setStatus(ivhProgressResponse.getStatus());
            result.setFailCode(ivhProgressResponse.getStatus());
            result.setMediaUrl(ivhProgressResponse.getMediaUrl());
            result.setFailMessage(ivhProgressResponse.getFailMessage());
        } else {
            ++count;
            if (count > 100) {
                log.error("腾讯云tts查询合成时间过久！taskId={},count={}", taskId, count);
                throw BusinessServiceException.getInstance("腾讯云tts合成超时");
            }
            getProcessResult(result, taskId, count);
        }
    }

    @Override
    public CreateResponseDTO videoMake(IvhVideoMakeRequestDTO request) {
        IvhVideoMakeRequest ivhListenTtsRequest = IvhVideoMakeRequest.builder().build();
        BeanUtils.copyProperties(request, ivhListenTtsRequest);
        IvhBaseResponse<IvhTask> resp = ivhDigitalClient.videoMake(
                IvhBaseRequest.<IvhVideoMakeRequest>builder().payload(ivhListenTtsRequest).build());
        CreateResponseDTO createResponseDTO = new CreateResponseDTO();
        if (resp.isSuccess()) {
            createResponseDTO.setTaskId(resp.getPayload().getTaskId());
        }
        return createResponseDTO;
    }

    @Override
    public ResultModel<IvhSmallSampleAnchorResponseDTO> smallSampleAnchorList(PageRequestDTO pageRequest) {
        IvhGetSmallSampleAnchorRequest anchorRequest = new IvhGetSmallSampleAnchorRequest();
        anchorRequest.setPageIndex(pageRequest.getPageIndex());
        anchorRequest.setPageSize(pageRequest.getPageSize());
        IvhBaseResponse<IvhGetSmallSampleAnchorResponse> resp = ivhDigitalSmallSampleClient.listAnchors(
                IvhBaseRequest.<IvhGetSmallSampleAnchorRequest>builder().payload(anchorRequest).build());
        if (!resp.isSuccess() || Objects.equals(Const.ZERO, resp.getPayload().getTotal())) {
            log.error("ivhDigitalSmallSampleClient.listAnchors异常或空!resp={}", JSONUtil.toJsonStr(resp));
            return ResultModel.success(null);
        }
        List<IvhSmallSampleVirtualMan> virtualManList = resp.getPayload().getVirtualMans();
        List<IvhSmallSampleVirtualManDTO> collect =
                virtualManList.stream().map(x -> convert(x)).collect(Collectors.toList());
        IvhSmallSampleAnchorResponseDTO anchorResp = new IvhSmallSampleAnchorResponseDTO();
        anchorResp.setVirtualMans(collect);
        anchorResp.setTotal(resp.getPayload().getTotal());
        return ResultModel.success(anchorResp);
    }

    private IvhSmallSampleVirtualManDTO convert(IvhSmallSampleVirtualMan source) {
        IvhSmallSampleVirtualManDTO ivhSmallSampleVirtualManDTO = new IvhSmallSampleVirtualManDTO();
        ivhSmallSampleVirtualManDTO.setVirtualManKey(source.getVirtualManKey());
        ivhSmallSampleVirtualManDTO.setAnchorName(source.getAnchorName());
        ivhSmallSampleVirtualManDTO.setAnchorCode(source.getAnchorCode());
        ivhSmallSampleVirtualManDTO.setHeaderImage(source.getHeaderImage());
        ivhSmallSampleVirtualManDTO.setSupportDriverTypes(source.getSupportDriverTypes());
        ivhSmallSampleVirtualManDTO.setClothesName(source.getClothesName());
        ivhSmallSampleVirtualManDTO.setPoseName(source.getPoseName());
        ivhSmallSampleVirtualManDTO.setResolution(source.getResolution());
        ivhSmallSampleVirtualManDTO.setPoseImage(source.getPoseImage());
        ivhSmallSampleVirtualManDTO.setClothesImage(source.getClothesImage());
        ivhSmallSampleVirtualManDTO.setExpireDate(source.getExpireDate());
        IvhGetTimbreRequest request =
                IvhGetTimbreRequest.builder().virtualmanKey(ivhSmallSampleVirtualManDTO.getVirtualManKey()).build();
        IvhBaseResponse<IvhTimbreResponse> resp = ivhDigitalSmallSampleClient.getTimbre(
                IvhBaseRequest.<IvhGetTimbreRequest>builder().payload(request).build());
        if (resp.isSuccess() && CollectionUtils.isNotEmpty(resp.getPayload().getTimbres())) {
            List<IvhTimbres> timbres = resp.getPayload().getTimbres();
            ivhSmallSampleVirtualManDTO.setTimbres(timbres.stream().map(x -> convert(x)).collect(Collectors.toList()));
        }
        return ivhSmallSampleVirtualManDTO;
    }

    private IvhTimbreDetailDTO convert(IvhTimbres source) {
        IvhTimbreDetailDTO ivhTimbresDTO = new IvhTimbreDetailDTO();
        ivhTimbresDTO.setTimbreKey(source.getTimbreKey());
        ivhTimbresDTO.setTimbreName(source.getTimbreName());
        ivhTimbresDTO.setTimbreSample(source.getTimbreSample());
        ivhTimbresDTO.setTimbreDesc(source.getTimbreDesc());
        return ivhTimbresDTO;
    }

    private IvhUploadTrainResponse getuploadcredentials() {
        IvhBaseResponse<IvhUploadTrainResponse> resp = ivhDigitalTrainClient.getuploadcredentials(
                IvhBaseRequest.<IvhTask>builder().payload(new IvhTask()).build());
        if (resp.isSuccess()) {
            return resp.getPayload();
        }
        throw BusinessServiceException.getInstance("上传视频cos失败");
    }

    private String uploadCOS(String mediaUrl, String type) {
        log.info("上传cos文件，地址：{},类型{}", mediaUrl, type);
        IvhUploadTrainResponse payload = getuploadcredentials();
        // 1 传入获取到的临时密钥 (tmpSecretId, tmpSecretKey, sessionToken)
        String tmpSecretId = payload.getCredentials().getTmpSecretId();
        String tmpSecretKey = payload.getCredentials().getTmpSecretKey();
        String sessionToken = payload.getCredentials().getToken();
        BasicSessionCredentials cred = new BasicSessionCredentials(tmpSecretId, tmpSecretKey, sessionToken);

        CosBean cosBean = CosUtil.buildCosByPathPrefix(payload.getPathPrefix());

        Region region = new Region(
                cosBean.getRegion()); //COS_REGION 参数：配置成存储桶 bucket 的实际地域，例如 ap-beijing，更多 COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(region);
        COSClient cosClient = new COSClient(cred, clientConfig);
        String fileName = type + UUID.randomUUID().toString().replace("-", "") + ".mp4";
        File file = downloadFromUrl(mediaUrl, fileName);

        try {
            PutObjectRequest putObjectRequest =
                    new PutObjectRequest(cosBean.getBucketName(), cosBean.getKey() + fileName, file);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.warn("cos upload requestId={}", putObjectResult.getRequestId());
        } catch (Exception e) {
            log.error("", e);
        } finally {
            FileUtil.del(file);
            cosClient.shutdown();
        }
        return payload.getPathPrefix() + fileName;
    }

    public static File downloadFromUrl(String url, String fileName) {
        File f = null;
        try {
            f = new File(dir() + fileName);
            FileUtils.copyURLToFile(new URL(url), f);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return f;
    }

    private static File dir() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String subDir = sdf.format(new Date());
        File dir = new File(subDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }

    @Override
    public ResultModel<Object> getServiceAssetInfo() {
        IvhGetServiceAssetRequest ivhGetServiceAssetRequest = new IvhGetServiceAssetRequest();
        ivhGetServiceAssetRequest.setPageIndex(Const.ONE);
        ivhGetServiceAssetRequest.setPageSize(Const.ONE_HUNDRED);
        IvhBaseResponse<IvhServiceAssetResponse> serviceAsset = ivhDigitalSmallSampleClient.getServiceAsset(
                IvhBaseRequest.<IvhGetServiceAssetRequest>builder().payload(ivhGetServiceAssetRequest).build());

        return ResultModel.success(serviceAsset);
    }

    public static List<SentencesDTO> cnvTextTimestampResult(List<IvhSentences> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return null;
        }
        List<SentencesDTO> resultList = inputList.stream().map(input -> {
            SentencesDTO result = new SentencesDTO();
            result.setSentence(input.getSentence());
            //result.setSentence(input.getSentence().replaceAll(" ", ""));
            result.setWords(input.getWords().stream().map(ivhWords -> {
                WordsDTO wordsDTO = new WordsDTO();
                wordsDTO.setWord(ivhWords.getWord());
                //wordsDTO.setWord(ivhWords.getWord().replaceAll(" ", ""));
                wordsDTO.setStartTime(ivhWords.getStartTimestamp() / 10000);
                wordsDTO.setEndTime(ivhWords.getEndTimestamp() / 10000);
                return wordsDTO;
            }).collect(Collectors.toList()));
            return result;
        }).collect(Collectors.toList());

        return resultList;
    }
}


