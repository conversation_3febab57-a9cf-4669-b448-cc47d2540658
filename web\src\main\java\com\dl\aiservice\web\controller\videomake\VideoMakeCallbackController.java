package com.dl.aiservice.web.controller.videomake;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.VideoTaskJobPO;
import com.dl.aiservice.biz.manager.VideoTaskJobManager;
import com.dl.aiservice.biz.service.videomake.VideoMakeService;
import com.dl.aiservice.biz.service.videomake.VideoMakeServiceHelper;
import com.dl.aiservice.biz.service.videomake.enums.VideoMakeJobErrStatusEnum;
import com.dl.aiservice.biz.service.videomake.jimu.bo.InnerErrNotifyParamBO;
import com.dl.aiservice.share.digitalman.DigitalManCallbackDTO;
import com.dl.aiservice.share.digitalman.DigitalManVideoGenResultDTO;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName VideoMakeController
 * @Description 快视频合成请求回调处理类
 * <AUTHOR>
 * @Date 2023/4/13 16:22
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/video/make/callback")
public class VideoMakeCallbackController {

    @Resource
    private VideoTaskJobManager videoTaskJobManager;
    @Resource
    private VideoMakeServiceHelper videoMakeServiceHelper;

    /**
     * 第三方数字人视频合成回调
     *
     * @param param
     * @return
     */
    @PostMapping("/dg")
    @NotAuth
    public ResultModel<Boolean> dgCallback(@RequestBody DigitalManCallbackDTO param) {
        log.info("dgCallback>>param>>>{}", JSONUtil.toJsonStr(param));
        List<DigitalManVideoGenResultDTO> videoList = param.getVideoList();
        DigitalManVideoGenResultDTO processing = videoList.stream()
                .filter(x -> Objects.equals(x.getStatus(), Const.ONE) || Objects.equals(x.getStatus(), Const.TWO))
                .findAny()
                .orElse(null);
        if (Objects.nonNull(processing)) {
            return ResultModel.success(Boolean.TRUE);
        }
        VideoTaskJobPO taskJob =
                videoTaskJobManager.lambdaQuery().eq(VideoTaskJobPO::getJobId, param.getVideoTaskJobId()).one();
        if (Objects.isNull(taskJob)) {
            log.info("dgCallback>>VideoTaskJobPO not exist!>>param>>>{}", JSONUtil.toJsonStr(param));
            return ResultModel.success(Boolean.TRUE);
        }
        MediaProduceChannelEnum channelEnum = MediaProduceChannelEnum.getByCode(taskJob.getChannel());
        VideoMakeService videoMakeService = videoMakeServiceHelper.get(channelEnum);
        DigitalManVideoGenResultDTO genFailed =
                videoList.stream().filter(x -> Objects.equals(x.getStatus(), -Const.ONE)).findAny().orElse(null);
        if (Objects.nonNull(genFailed)) {
            // 存在合成失败的任务
            log.error("dgCallback>>mediaProduceJob>>{}", JsonUtils.toJSON(genFailed));
            videoMakeService.innerErrNotify(buildErrNotifyParam(taskJob, VideoMakeJobErrStatusEnum.CODE_56.getCode(),
                    genFailed.getFailReason()));
            return ResultModel.success(Boolean.TRUE);
        }
        try {
            videoMakeService.afterThirdVirtualManDone(taskJob.getTenantCode(), taskJob.getJobId(), videoList);
        } catch (Exception e) {
            log.error("dgCallback>>afterThirdVirtualManDone>>taskJobId={}", taskJob.getJobId(), e);
            videoMakeService.innerErrNotify(
                    buildErrNotifyParam(taskJob, VideoMakeJobErrStatusEnum.CODE_55.getCode(), e.getMessage()));
        }
        return ResultModel.success(Boolean.TRUE);
    }

    private InnerErrNotifyParamBO buildErrNotifyParam(VideoTaskJobPO taskJob, Integer status, String failReason) {
        return InnerErrNotifyParamBO.builder()
                .videoTaskJobId(taskJob.getJobId())
                .worksBizId(taskJob.getWorksBizId())
                .jobStatus(status)
                .failReason(failReason)
                .callbackUrl(taskJob.getCallbackUrl())
                .build();
    }
}
