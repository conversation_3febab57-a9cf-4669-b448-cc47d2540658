package com.dl.aiservice.biz.manager.digitalasset.conf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-14 10:33
 */
@Component
public class DaOldNewBizIdConfig {

    /**
     * 新老数字人bizId映射 key-老bizId， value-新bizId
     */
    @Value("#{${da.old-new-biz-id.vm.map}}")
    private Map<Long, Long> vmOldNewBizIdMap = new HashMap<>();

    /**
     * 新老声音bizId映射 key-老bizId， value-新bizId
     */
    @Value("#{${da.old-new-biz-id.voice.map}}")
    private Map<Long, Long> voiceOldNewBizIdMap = new HashMap<>();

    public Map<Long, Long> getVmOldNewBizIdMap() {
        return vmOldNewBizIdMap;
    }

    public void setVmOldNewBizIdMap(Map<Long, Long> vmOldNewBizIdMap) {
        this.vmOldNewBizIdMap = vmOldNewBizIdMap;
    }

    public Map<Long, Long> getVoiceOldNewBizIdMap() {
        return voiceOldNewBizIdMap;
    }

    public void setVoiceOldNewBizIdMap(Map<Long, Long> voiceOldNewBizIdMap) {
        this.voiceOldNewBizIdMap = voiceOldNewBizIdMap;
    }
}
