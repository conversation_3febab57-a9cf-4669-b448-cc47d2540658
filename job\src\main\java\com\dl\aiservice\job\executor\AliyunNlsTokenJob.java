package com.dl.aiservice.job.executor;

import com.alibaba.nls.client.AccessToken;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunDslConfig;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunDslConst.ACCESS_TOKEN_REDIS_KEY;
import static com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunDslConst.LOCK_TIME;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 13:53
 */
@Component
public class AliyunNlsTokenJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunNlsTokenJob.class);

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AliyunDslConfig aliyunDslConfig;

    /**
     * 刷新阿里云智能语音交互服务的token
     */
    @XxlJob("refreshAliyunNlsTokenJobHandler")
    public void refreshAliyunNlsTokenJobHandler() {
        AccessToken accessToken = new AccessToken(aliyunDslConfig.getAccessKeyId(),
                aliyunDslConfig.getAccessKeySecret());
        try {
            accessToken.apply();
        } catch (Exception e) {
            LOGGER.error("阿里云智能语音交互token获取失败,e:{}", e);
            throw BusinessServiceException.getInstance("阿里云智能语音交互token获取失败");
        }
        String token = accessToken.getToken();
        long expireTime = accessToken.getExpireTime();
        LOGGER.info("阿里云智能语音交互token,,,,token:{},,,,expireTime:{}", token, expireTime);
        redisUtil.set(ACCESS_TOKEN_REDIS_KEY, token, LOCK_TIME);
    }
}
