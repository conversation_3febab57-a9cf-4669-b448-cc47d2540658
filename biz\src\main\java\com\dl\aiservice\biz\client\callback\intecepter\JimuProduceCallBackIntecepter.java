package com.dl.aiservice.biz.client.callback.intecepter;

import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.share.videoproduce.dto.jimu.JobResultDTO;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public class JimuProduceCallBackIntecepter implements Interceptor {

    private static final String RESP_CODE = "code";

    private static final String JOB_ID_VALUE = "jobId";
    private static final String RESULT_VALUE = "jobResult";

    private static final String PRODUCE_HANDLE = "/jimu";


    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        Interceptor.super.onSuccess(data, request, response);
        videoProcessCallBack(data, request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        videoProcessCallBack(ex, request, response);
        throw BusinessServiceException.getInstance(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString(), ex.getMessage());
    }

    private void videoProcessCallBack(Object data, ForestRequest request, ForestResponse response) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (!StringUtils.equals(PRODUCE_HANDLE, url)) {
            return;
        }
        MediaProduceJobManager manager = ApplicationContextUtils.getContext().getBean(MediaProduceJobManager.class);
        String jobId = (String) request.getVariableValue(JOB_ID_VALUE);
        List<MediaProduceJobPO> list = manager.lambdaQuery().eq(MediaProduceJobPO::getExtJobId, jobId).list();
        if(CollectionUtils.isEmpty(list)){
            log.info("未查询到该jobId记录,request={}", JsonUtils.toJSON(request));
            return;
        }
        MediaProduceJobPO job = list.get(0);

        JobResultDTO jobResult = (JobResultDTO)request.getVariableValue(RESULT_VALUE);
        if(Objects.nonNull(jobResult)){
            job.setStatus(Const.ZERO);
            job.setMediaUrl(jobResult.getUrl());
            job.setDuration(jobResult.getDuration());
        }
        manager.save(job);
    }
}
